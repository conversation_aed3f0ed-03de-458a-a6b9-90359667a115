{
  "SlackAlert": {
    "ApiToken": "xoxb-your-slack-bot-token-here",
    "DefaultChannel": "alerts-ms-fr",
    "SendMessageUrl": "https://slack.com/api/chat.postMessage",
    "BotName": "<PERSON>rro<PERSON>",
    "BotEmoji": ":warning:",
    "MaxRetryAttempts": 3,
    "RetryInitialDelayMs": 1000,
    "TimeoutMs": 10000,
    "IncludeStackTrace": true,
    "MaxMessageLength": 15000,
    "EnableFallbackLogging": true,
    "CircuitBreakerThreshold": 5,
    "CircuitBreakerDurationSeconds": 60,
    "Enabled": true,
    "DefaultAllowedEnvironments": ["preprod", "prod"],
    "DefaultAllowedCountries": ["fr", "it"]
  }
}

/*
Environment filtering configuration:
- DefaultAllowedEnvironments: List of environments that should receive alerts by default
  Supported values: "dev", "recette", "perf", "preprod", "prod", "development"
  
- DefaultAllowedCountries: List of countries that should receive alerts by default  
  Supported values: "fr", "it", "es", "pt", "dk", "se"

Dynamic channel generation:
- When no channel is specified in method calls, the service will generate channel names using:
  Pattern: alerts-ms-{country}-{env}
  Special cases:
  - Production: alerts-ms-{country} (removes -env suffix)
  - Development: alerts-ms-debug (ignores country)
  
Examples:
- France + Prod: alerts-ms-fr
- Italy + Dev: alerts-ms-it-dev  
- Any country + Development: alerts-ms-debug

Environment variables required:
- ASPNETCORE_ENVIRONMENT: dev, recette, perf, preprod, prod, development
- ASPNETCORE_COUNTRY: fr, it, es, pt, dk, se
*/
