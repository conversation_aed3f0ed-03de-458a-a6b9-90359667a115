﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using System.Xml.Linq;
using Amazon.Runtime.Internal.Transform;
using ITF.SharedLibraries.EnvironmentVariable;
using ITF.SharedLibraries.ExtensionMethods;
using ITF.SharedLibraries.HttpClient.Authentication;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.AspNetCore.WebUtilities;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Primitives;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Serialization;
using Polly;
using static ITF.SharedLibraries.ExtensionMethods.Serializer;

namespace ITF.SharedLibraries.HttpClient
{
    public abstract class HttpClient : IHttpClient
    {
        public System.Net.Http.HttpClient _client { get; private set; }
        public Uri _uri { get; private set; }
        public string _endpoint { get; private set; }
        public Authentication.Authentication _authentication { get; private set; }
        public static string _token { get; private set; }
        private static readonly SemaphoreSlim AccessTokenSemaphore = new(1, 1);
        public static DateTime _tokenExpiration { get; private set; }
        public ILogger _logger { get; private set; }
        private static string _soapLoginTemplate;

        protected HttpClient(
            System.Net.Http.HttpClient httpClient,
            IConfiguration config,
            string varEnv,
            ILogger logger = null)
        {
            var configuration = config.Get<Configuration>(varEnv);

            _uri = !string.IsNullOrEmpty(configuration.Url) ? new Uri(configuration.Url) : null;
            _endpoint = configuration.Endpoint;

            if (configuration.Authentication != null)
                _authentication = configuration.Authentication;

            _client = httpClient;
            _logger = logger;

            if (string.IsNullOrWhiteSpace(_token) || _authentication?.Token != _token)
                _token = _authentication?.Token;

            if (string.IsNullOrWhiteSpace(_token) == false)
            {
                if (_authentication.AuthMethod == AuthMethod.BASIC_KEY.ToString())
                    _client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Basic", _token);
                else if (_authentication.AuthMethod == AuthMethod.JWT_KEY.ToString())
                    _client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _token);
            }
        }

        public static void SetSoapLoginTemplate(string soapLoginTemplate) => _soapLoginTemplate = soapLoginTemplate;

        public async Task<HttpResponseMessage> GetAsync(Dictionary<string, string> headers, string? endpoint = null, string? queryParameters = null, int maxRetries = 3)
        {
            try
            {
                if (_uri is null && endpoint is null)
                    throw new ArgumentNullException(nameof(endpoint), "no uri/endpoint found in the param");

                // Create the URI and request message outside the retry logic
                var uri = new Uri(CombineUri(_uri?.ToString() ?? endpoint, _uri is null ? null : endpoint ?? _endpoint, queryParameters));

                return await ExecuteWithRetryAsync(async () =>
                {
                    try
                    {
                        // Create a new request message for each attempt (can't reuse HttpRequestMessage objects)
                        var requestMessage = new HttpRequestMessage(HttpMethod.Get, uri);

                        // Add headers
                        if (headers != null)
                        {
                            foreach (KeyValuePair<string, string> entry in headers)
                            {
                                requestMessage.Headers.TryAddWithoutValidation(entry.Key, entry.Value);
                            }
                        }

                        // Authentication handling
                        if (_authentication != null)
                        {
                            await TokenHandlingAsync();
                        }

                        // Send the request
                        return await _client.SendAsync(requestMessage);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Exception during HTTP GET operation: {message}", ex.Message);
                        throw;
                    }
                }, maxRetries);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to perform GET request with headers: {message}", ex.Message);
                throw;
            }
        }
        public async Task<HttpResponseMessage> GetAsync(string? endpoint = null, string? queryParameters = null, int maxRetries = 3)
        {
            try
            {
                if (_uri is null && endpoint is null)
                    throw new ArgumentNullException(nameof(endpoint), "no uri/endpoint found in the param");

                var uri = new Uri(CombineUri(_uri?.ToString() ?? endpoint, _uri is null ? null : endpoint ?? _endpoint, queryParameters));

                return await ExecuteWithRetryAsync(async () =>
                {
                    try
                    {
                        // Authentication handling
                        if (_authentication?.AuthMethod == AuthMethod.QUERY_PARAM.ToString())
                        {
                            Uri enrichedUri = GetUriForQueryParamAuthMethod(endpoint, queryParameters);
                            return await _client.GetAsync(enrichedUri);
                        }
                        else if (_authentication?.AuthMethod == AuthMethod.XWSSE.ToString())
                        {
                            var header = XwsseHeaderHandling();
                            _client.DefaultRequestHeaders.Remove("X-WSSE"); // Remove any existing header before adding
                            _client.DefaultRequestHeaders.Add("X-WSSE", $"UsernameToken {header}");
                            return await _client.GetAsync(uri);
                        }
                        else if (_authentication != null)
                        {
                            await TokenHandlingAsync();
                            return await _client.GetAsync(uri);
                        }

                        // No authentication
                        return await _client.GetAsync(uri);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Exception during HTTP GET operation: {message}", ex.Message);
                        throw;
                    }
                }, maxRetries);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to perform GET request: {message}", ex.Message);
                throw;
            }
        }
        public async Task<T?> GetAsync<T>(string? endpoint = null, string? queryParameters = null, Serializer.SerializerType serializerType = Serializer.SerializerType.TextJson)
        {
            var res = await GetAsync(endpoint, queryParameters);

            if (!res.IsSuccessStatusCode)
                return default;

            var resString = await res.Content.ReadAsStringAsync();

            return resString.Deserialize<T>(serializerType);
        }

        public async Task<string?> GetAsyncString(string? endpoint = null, string? queryParameters = null)
        {
            var res = await GetAsync(endpoint, queryParameters);

            if (!res.IsSuccessStatusCode)
                return default;

            return await res.Content.ReadAsStringAsync();
        }

        public async Task<HttpResponseMessage> GetOdataAsync(string? endpoint = null, string? queryParameters = null)
        {
            var uri = new Uri(CombineUri(_uri.ToString(), endpoint ?? _endpoint, queryParameters));

            try
            {
                // Authentication needed
                if (_authentication != null)
                    await TokenHandlingAsync();

                return await _client.GetAsync(uri);
            }
            catch (Exception e)
            {
                _logger?.LogError(e, "Error in {method}", nameof(GetOdataAsync));
                throw;
            }
        }

        public async Task<HttpResponseMessage> PostAsync(object objectToPost,string? endpoint = null,string? queryParameters = null,Serializer.SerializerType serializerType = Serializer.SerializerType.TextJson,string contentType = "application/json",JsonSerializerOptions? options = null,int maxRetries = 3)
        {
            try
            {
                if (_uri is null && endpoint is null)
                    throw new ArgumentNullException(nameof(endpoint),"no uri/endpoint found in the param");

                if (objectToPost is null)
                    throw new ArgumentNullException(nameof(objectToPost), "object provided is null");

                var uri = new Uri(CombineUri(_uri?.ToString() ?? endpoint, _uri is null ? null : endpoint ?? _endpoint, queryParameters));

                // Prepare content outside the retry logic to avoid unnecessary serialization
                StringContent content;
                try
                {
                    content =  new StringContent(objectToPost.Serialize(serializerType, options: options) ?? "", Encoding.UTF8, contentType);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to serialize request content: {message}", ex.ToString());
                    throw new InvalidOperationException("Failed to serialize request content", ex);
                }

                return await ExecuteWithRetryAsync(async () =>
                {
                    try
                    {
                        // Authentication handling
                        if (_authentication?.AuthMethod == AuthMethod.QUERY_PARAM.ToString())
                        {
                            Uri enrichedUri = GetUriForQueryParamAuthMethod(endpoint, queryParameters);
                            return await _client.PostAsync(enrichedUri, content);
                        }
                        else if (_authentication?.AuthMethod == AuthMethod.XWSSE.ToString())
                        {
                            var header = XwsseHeaderHandling();
                            _client.DefaultRequestHeaders.Remove("X-WSSE"); // Remove any existing header before adding
                            _client.DefaultRequestHeaders.Add("X-WSSE", $"UsernameToken {header}");
                            return await _client.PostAsync(uri, content);
                        }
                        else if (_authentication != null)
                        {
                            await TokenHandlingAsync();
                            return await _client.PostAsync(uri, content);
                        }

                        // No authentication
                        return await _client.PostAsync(uri, content);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Exception during HTTP operation: {message}", ex.ToString());
                        throw;
                    }
                }, maxRetries);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to perform POST request: {message}", ex.ToString());
                throw;
            }
        }

        public async Task<HttpResponseMessage> PatchAsync(object objectToPatch, string? endpoint = null, string? queryParameters = null,Serializer.SerializerType serializerType = Serializer.SerializerType.TextJson, string contentType = "application/json",JsonSerializerOptions? options = null, int maxRetries = 3)
        {
            try
            {
                if (_uri is null && endpoint is null)
                    throw new ArgumentNullException(nameof(endpoint), "no uri/endpoint found in the param");

                if (objectToPatch is null)
                    throw new ArgumentNullException(nameof(objectToPatch), "object provided is null");

                var uri = new Uri(CombineUri(_uri?.ToString() ?? endpoint, _uri is null ? null : endpoint ?? _endpoint, queryParameters));

                // Prepare content outside the retry logic to avoid unnecessary serialization
                StringContent content;
                try
                {
                    content = new StringContent(objectToPatch.Serialize(serializerType, options: options) ?? "", Encoding.UTF8, contentType);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to serialize request content for PATCH: {message}", ex.Message);
                    throw new InvalidOperationException("Failed to serialize request content for PATCH", ex);
                }

                return await ExecuteWithRetryAsync(async () =>
                {
                    try
                    {
                        // Authentication handling
                        if (_authentication?.AuthMethod == AuthMethod.QUERY_PARAM.ToString())
                        {
                            Uri enrichedUri = GetUriForQueryParamAuthMethod(endpoint, queryParameters);
                            return await _client.PatchAsync(enrichedUri, content);
                        }
                        else if (_authentication?.AuthMethod == AuthMethod.XWSSE.ToString())
                        {
                            var header = XwsseHeaderHandling();
                            _client.DefaultRequestHeaders.Remove("X-WSSE"); // Remove any existing header before adding
                            _client.DefaultRequestHeaders.Add("X-WSSE", $"UsernameToken {header}");
                            return await _client.PatchAsync(uri, content);
                        }
                        else if (_authentication != null)
                        {
                            await TokenHandlingAsync();
                            return await _client.PatchAsync(uri, content);
                        }

                        // No authentication
                        return await _client.PatchAsync(uri, content);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Exception during HTTP PATCH operation: {message}", ex.Message);
                        throw;
                    }
                }, maxRetries);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to perform PATCH request: {message}", ex.Message);
                throw;
            }
        }

        public async Task<HttpResponseMessage> PutAsync(object objectToPut, string? endpoint = null, string? queryParameters = null,Serializer.SerializerType serializerType = Serializer.SerializerType.TextJson, string contentType = "application/json",JsonSerializerOptions? options = null, int maxRetries = 3)
        {
            try
            {
                if (_uri is null && endpoint is null)
                    throw new ArgumentNullException(nameof(endpoint), "no uri/endpoint found in the param");

                if (objectToPut is null)
                    throw new ArgumentNullException(nameof(objectToPut), "object provided is null");

                var uri = new Uri(CombineUri(_uri?.ToString() ?? endpoint, _uri is null ? null : endpoint ?? _endpoint, queryParameters));

                // Prepare content outside the retry logic to avoid unnecessary serialization
                StringContent content;
                try
                {
                    content = new StringContent(objectToPut.Serialize(serializerType, options: options) ?? "", Encoding.UTF8, contentType);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to serialize request content for PUT: {message}", ex.Message);
                    throw new InvalidOperationException("Failed to serialize request content for PUT", ex);
                }

                return await ExecuteWithRetryAsync(async () =>
                {
                    try
                    {
                        // Authentication handling
                        if (_authentication?.AuthMethod == AuthMethod.QUERY_PARAM.ToString())
                        {
                            Uri enrichedUri = GetUriForQueryParamAuthMethod(endpoint, queryParameters);
                            return await _client.PutAsync(enrichedUri, content);
                        }
                        else if (_authentication?.AuthMethod == AuthMethod.XWSSE.ToString())
                        {
                            var header = XwsseHeaderHandling();
                            _client.DefaultRequestHeaders.Remove("X-WSSE"); // Remove any existing header before adding
                            _client.DefaultRequestHeaders.Add("X-WSSE", $"UsernameToken {header}");
                            return await _client.PutAsync(uri, content);
                        }
                        else if (_authentication != null)
                        {
                            await TokenHandlingAsync();
                            return await _client.PutAsync(uri, content);
                        }

                        // No authentication
                        return await _client.PutAsync(uri, content);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Exception during HTTP PUT operation: {message}", ex.Message);
                        throw;
                    }
                }, maxRetries);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to perform PUT request: {message}", ex.Message);
                throw;
            }
        }

        public async Task<HttpResponseMessage> PatchAsync(string? endpoint = null, string? queryParameters = null, int maxRetries = 3)
        {
            try
            {
                if (_uri is null && endpoint is null)
                    throw new ArgumentNullException(nameof(endpoint), "no uri/endpoint found in the param");

                var uri = new Uri(CombineUri(_uri?.ToString() ?? endpoint, _uri is null ? null : endpoint ?? _endpoint, queryParameters));

                return await ExecuteWithRetryAsync(async () =>
                {
                    try
                    {
                        // Create a new request message for each attempt
                        var request = new HttpRequestMessage(new HttpMethod("PATCH"), uri);

                        // Authentication handling
                        if (_authentication != null)
                            await TokenHandlingAsync();

                        return await _client.SendAsync(request);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Exception during HTTP PATCH operation: {message}", ex.Message);
                        throw;
                    }
                }, maxRetries);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to perform PATCH request: {message}", ex.Message);
                throw;
            }
        }
        public async Task<HttpResponseMessage> DeleteAsync(string? endpoint = null, string? queryParameters = null, int maxRetries = 3)
        {
            try
            {
                if (_uri is null && endpoint is null)
                    throw new ArgumentNullException(nameof(endpoint), "no uri/endpoint found in the param");

                var uri = new Uri(CombineUri(_uri?.ToString() ?? endpoint, _uri is null ? null : endpoint ?? _endpoint, queryParameters));

                return await ExecuteWithRetryAsync(async () =>
                {
                    try
                    {
                        // Create a new request message for each attempt
                        var request = new HttpRequestMessage(new HttpMethod("DELETE"), uri);

                        // Authentication handling
                        if (_authentication != null)
                            await TokenHandlingAsync();

                        return await _client.SendAsync(request);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Exception during HTTP DELETE operation: {message}", ex.Message);
                        throw;
                    }
                }, maxRetries);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to perform DELETE request: {message}", ex.Message);
                throw;
            }
        }

      
        public async Task<HttpResponseMessage> PutAsync(string? endpoint = null, string? queryParameters = null, int maxRetries = 3)
        {
            try
            {
                if (_uri is null && endpoint is null)
                    throw new ArgumentNullException(nameof(endpoint), "no uri/endpoint found in the param");

                var uri = new Uri(CombineUri(_uri?.ToString() ?? endpoint, _uri is null ? null : endpoint ?? _endpoint, queryParameters));

                return await ExecuteWithRetryAsync(async () =>
                {
                    try
                    {
                        // Create a new request message for each attempt
                        var request = new HttpRequestMessage(new HttpMethod("PUT"), uri);

                        // Authentication handling
                        if (_authentication != null)
                            await TokenHandlingAsync();

                        return await _client.SendAsync(request);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Exception during HTTP PUT operation: {message}", ex.Message);
                        throw;
                    }
                }, maxRetries);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to perform PUT request: {message}", ex.Message);
                throw;
            }
        }

        public async Task<T> ReadInformationsAsync<T>(HttpResponseMessage response, Serializer.SerializerType serializerType = Serializer.SerializerType.TextJson, JsonSerializerSettings settings = null) where T : class
        {
            if (!response.IsSuccessStatusCode)
                throw new Exception($"{nameof(ReadInformationsAsync)} has failed. HttpStatusCode {response.StatusCode} - Error : {response.Content.ReadAsStringAsync()?.Result}");

            var json = await response.Content.ReadAsStringAsync();

            if (typeof(T) == typeof(string))
                return json as T;

            return json.Deserialize<T>(serializerType, null, settings);
        }

        public async Task<T> ReadODATAInformationsAsync<T>(HttpResponseMessage response, string tokenValue, Serializer.SerializerType serializerType = Serializer.SerializerType.TextJson) where T : class
        {
            if (new[] { HttpStatusCode.Unauthorized, HttpStatusCode.Forbidden, HttpStatusCode.Redirect }.Contains(response.StatusCode))
                throw new TokenExpiredException($"The token has expired (httpstatus = {response.StatusCode}), redirecting to the login form");

            var json = await response.Content.ReadAsStringAsync();

            var html = json.Length < 100 ? json : json.Substring(0, 100);
            if (html.Contains("<!DOCTYPE html>"))
                throw new TokenExpiredException("The token has expired, redirecting to the login form");

            var jsonJObject = JObject.Parse(json);
            var jsonData = jsonJObject[tokenValue]?.ToString();

            if (jsonData is null)
                return default;

            if (typeof(T) == typeof(string))
                return jsonData as T;

            return jsonData.Deserialize<T>(serializerType);
        }

        public async Task<List<T>> GetAndReadOdataParallelSliceWithInFilterAsync<T>(string tokenValue, string filterField, List<string> filteredValues, int splitFrequency = 100, string endpoint = null, Serializer.SerializerType serializerType = Serializer.SerializerType.TextJson, int nbSubBatch = 2) where T : class
        {
            var tasks = new List<Task<List<T>>>();
            foreach (var batch in filteredValues.GetBatch(splitFrequency))
            {
                // Enqueue the task of parallel slice per sub-batch 
                tasks.Add(GetAndReadOdataParallelSliceAsync<T>(tokenValue, endpoint, $"?$filter={HttpUtility.UrlEncode(batch.ToOdataOrConditions(filterField))}", serializerType: serializerType, nbSubBatch: nbSubBatch));
            }
            var res = tasks.Where(t => t.IsFaulted).ToList();
            var result = await Task.WhenAll(tasks);

            var flatResults = result.ToList().SelectMany(r => r).ToList();
            return flatResults;
        }

        public async Task<List<T>> GetAndReadOdataParallelSliceAsync<T>(string tokenValue, string endpoint = null, string queryParameters = null, int odataSlice = 10000, Serializer.SerializerType serializerType = Serializer.SerializerType.TextJson, int nbSubBatch = 2) where T : class
        {
            var retryPolicy = Policy
                .Handle<TokenExpiredException>()
                .Or<TooManyRequestsException>()
                .WaitAndRetryAsync(5, retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)), async (e, i) =>
                {
                    _logger?.LogWarning("Polly : '{message}' at retry #{nbretry} : refresh token", e.Message, i);
                    if (e is TokenExpiredException)
                        await TokenHandlingAsync(true);
                });

            var results = new List<T>();
            var count = await Count(endpoint, queryParameters); //ex 20010

            if (count == 0)
                return results;

            Slice slice = new Slice { Top = count >= odataSlice ? odataSlice : count, Skip = 0 };
            var tasks = new List<Task<List<T>>>();
            do
            {
                //ex 1 : Count=20010 & Slice.Top=10000 & Slice.Skip=0
                //ex 2 : Count=10010 & Slice.Top=10000 & Slice.Skip=10000
                //ex 3 : Count=10 & Slice.Top=10 & Slice.Skip=20000
                tasks.Add(retryPolicy.ExecuteAsync(() => GetAndReadOdata<T>(tokenValue, slice, endpoint, queryParameters, serializerType: serializerType)));

                //ex 1 : Count=10010 & Slice.Top=10000 & Slice.Skip=0
                //ex 2 : Count=10 & Slice.Top=10000 & Slice.Skip=10000
                //ex 3 : Count=-10010 & Slice.Top=10 & Slice.Skip=20000
                count -= odataSlice;

                if (nbSubBatch > 0 &&
                    (tasks.Count >= nbSubBatch || count <= 0))
                {
                    var taskResults = await Task.WhenAll(tasks);

                    foreach (var r in taskResults)
                    {
                        results.AddRange(r);
                    }
                    tasks = new List<Task<List<T>>>();
                }

                // ex3 => break the loop
                if (count <= 0)
                    break;

                if (count < odataSlice)
                {
                    //ex 2 : Count=10 & Slice.Top=10 & Slice.Skip=20000
                    slice.Skip += odataSlice;
                    slice.Top = count;
                }
                else
                {
                    //ex 1 : Count=10010 & Slice.Top=10000 & Slice.Skip=10000
                    slice.Skip += odataSlice;
                }
            } while (true);

            // Case of no subBatch
            if (nbSubBatch == 0)
            {
                var taskAllResults = await Task.WhenAll(tasks);

                foreach (var r in taskAllResults)
                {
                    results.AddRange(r);
                }
            }

            return results;
        }

        public async Task<List<T>> GetAndReadOdata<T>(string tokenValue, Slice slice, string endpoint = null, string queryParameters = null, Serializer.SerializerType serializerType = Serializer.SerializerType.TextJson) where T : class
        {
            try
            {
                using var httpResults = await GetOdataAsync(endpoint, Combine(queryParameters, slice));

                // Get elements (and the slice if needed)
                if (httpResults.IsSuccessStatusCode)
                {
                    // Read elements
                    var results = await ReadODATAInformationsAsync<List<T>>(httpResults, tokenValue, serializerType);
                    return results;
                }
                else
                {
                    _logger?.LogError("{method} has failed. HttpStatusCode {HttpStatusCode} - Endpoint {endpoint} - Query {queryParameters} - Error : {response}", nameof(GetAndReadOdata), httpResults.StatusCode, endpoint, queryParameters, httpResults.Content.ReadAsStringAsync()?.Result);
                    if (httpResults.StatusCode == HttpStatusCode.TooManyRequests)
                    {
                        if (httpResults.Headers.Contains("Retry-After"))
                        {
                            int secondsToWait = int.Parse(httpResults.Headers.GetValues("Retry-After").FirstOrDefault());
                            _logger?.LogWarning("Waiting for {waitSecond} seconds regarding the retry after header", secondsToWait == 0 ? 30 : secondsToWait);
                            await Task.Delay(TimeSpan.FromSeconds(secondsToWait == 0 ? 30 : secondsToWait));

                            throw new TooManyRequestsException($"Too many requests error received, http client just waited for {(secondsToWait == 0 ? 30 : secondsToWait)} seconds");
                        }
                    }

                    throw new Exception($"{nameof(GetAndReadOdata)} has failed. HttpStatusCode {httpResults.StatusCode} - Endpoint {endpoint} - Query {queryParameters} - Error : {httpResults.Content.ReadAsStringAsync()?.Result}");
                }
            }
            catch (Exception e)
            {
                _logger?.LogError(e, "{method} has failed. Endpoint {endpoint} - Query {queryParameters}", nameof(GetAndReadOdata), endpoint, queryParameters);
                throw;
            }
        }
        public static async Task<byte[]> ReadAsByteArrayAsync(HttpResponseMessage response) => await response.Content.ReadAsByteArrayAsync();
        public async Task<int> Count(string endpoint = null, string queryParameters = null)
        {
            var retryPolicy = Policy
                .Handle<TokenExpiredException>()
                .Or<TooManyRequestsException>()
                .WaitAndRetryAsync(5, retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)), async (e, i) =>
                {
                    _logger?.LogWarning("Polly : '{message}' at retry #{nbretry} : refresh token", e.Message, i);
                    await TokenHandlingAsync(true);
                });

            return await retryPolicy.ExecuteAsync(async () => await CountWithPolicy(endpoint, queryParameters));
        }

        public void SetHeader(string key, string value)
        {
            try
            {
                AccessTokenSemaphore.WaitAsync().Wait();

                _client.DefaultRequestHeaders.Remove(key);
                _client.DefaultRequestHeaders.Add(key, value);
            }
            catch (Exception e)
            {
                _logger?.LogError(e, "Fail to set headers");
                throw;
            }
            finally
            {
                AccessTokenSemaphore.Release(1);
            }
        }

        public async Task SetHeaderAsync(string key, string value)
        {
            try
            {
                await AccessTokenSemaphore.WaitAsync();

                _client.DefaultRequestHeaders.Remove(key);
                _client.DefaultRequestHeaders.Add(key, value);
            }
            catch (Exception e)
            {
                _logger?.LogError(e, "Fail to set headers");
                throw;
            }
            finally
            {
                AccessTokenSemaphore.Release(1);
            }
        }

        public async Task<HttpResponseMessage> PostSoapAsync(string soapAction, string xml, string endpoint = null, string queryParameters = null)
        {
            var uri = new Uri(CombineUri(_uri.ToString(), endpoint ?? _endpoint, queryParameters));

            // Authentication needed
            if (_authentication != null)
            {
                await TokenHandlingAsync();

                var request = BuildSoapRequestMessage(xml, uri);
                await SetHeaderAsync("SOAPAction", soapAction);
                var response = await _client.SendAsync(request);

                // Should recall Token
                if (new[] { HttpStatusCode.Unauthorized, HttpStatusCode.Forbidden, HttpStatusCode.Redirect }.Contains(response.StatusCode))
                {
                    await TokenHandlingAsync(true);
                    await SetHeaderAsync("SOAPAction", soapAction);
                    return await _client.SendAsync(request);
                }
                return response;
            }

            await SetHeaderAsync("SOAPAction", soapAction);
            return await _client.SendAsync(BuildSoapRequestMessage(xml, uri));
        }

        private static HttpRequestMessage BuildSoapRequestMessage(string xml, Uri uri)
        {
            var request = new HttpRequestMessage(HttpMethod.Post, uri);
            request.Content = new StringContent(xml, Encoding.UTF8, "text/xml");
            return request;
        }

        private async Task<int> CountWithPolicy(string endpoint = null, string queryParameters = null)
        {
            // Get elements (and the slice if needed)
            using var response = await GetAsync($"{endpoint}/$count", queryParameters);

            if (new[] { HttpStatusCode.Unauthorized, HttpStatusCode.Forbidden, HttpStatusCode.Redirect }.Contains(response.StatusCode))
                throw new TokenExpiredException($"The token has expired (httpstatus = {response.StatusCode}), redirecting to the login form");

            if (response.StatusCode == HttpStatusCode.TooManyRequests)
            {
                if (response.Headers.Contains("Retry-After"))
                {
                    int secondsToWait = int.Parse(response.Headers.GetValues("Retry-After").FirstOrDefault());
                    _logger?.LogWarning("Waiting for {waitSecond} seconds regarding the retry after header", secondsToWait == 0 ? 30 : secondsToWait);
                    await Task.Delay(TimeSpan.FromSeconds(secondsToWait == 0 ? 30 : secondsToWait));

                    throw new TooManyRequestsException($"Too many requests error received, http client just waited for {(secondsToWait == 0 ? 30 : secondsToWait)} seconds");
                }
            }

            var result = await response.Content.ReadAsStringAsync();

            var html = result.Length < 100 ? result : result.Substring(0, 100);
            if (html.Contains("<!DOCTYPE html>"))
                throw new TokenExpiredException("The token has expired, redirecting to the login form");

            if (int.TryParse(result, out int number))
            {
                return number;
            }
            else
            {
                _logger?.LogError("{method} has failed. HttpStatusCode {HttpStatusCode} - Endpoint {endpoint} - Query {queryParameters} - Error : {response}", nameof(Count), response.StatusCode, endpoint, queryParameters, response.Content.ReadAsStringAsync()?.Result);
                throw new Exception($"{nameof(Count)} has failed. HttpStatusCode {response.StatusCode} - Endpoint {endpoint} - Query {queryParameters} - Error : {response.Content.ReadAsStringAsync()?.Result}");
            }
        }

        private async Task TokenHandlingAsync(bool force = false)
        {
            if (force || string.IsNullOrWhiteSpace(_token) ||
                (_authentication != null && (_authentication.AuthMethod == AuthMethod.JWT_KEY.ToString() || _authentication.AuthMethod == AuthMethod.BASIC_KEY.ToString()) && _authentication.Token != _token))
                await GetTokenAsync();
            else if (
                        (_authentication.AuthMethod == AuthMethod.OAUTH.ToString() ||
                        _authentication.AuthMethod == AuthMethod.JWT.ToString() ||
                        _authentication.AuthMethod == AuthMethod.SOAP.ToString() ||
                        _authentication.UseExpirationTime)
                    &&
                        (_tokenExpiration == default || DateTime.Now > _tokenExpiration)
                    && _authentication != null
                    )
                await GetTokenAsync();
            else
            {
                // set only the headers for the request
                await GetTokenAsync(true);
            }
            return;
        }

        private static string Combine(string parameters, Slice slice)
        {
            if (slice is null || (slice.Top == 0 && slice.Skip == 0))
                return parameters;

            if (parameters is null)
                return $"?$skip={slice.Skip}&$top={slice.Top}";

            return $"{parameters}&$skip={slice.Skip}&$top={slice.Top}";
        }

        private async Task GetTokenAsync(bool OnlyHeader = false)
        {
            try
            {
                await AccessTokenSemaphore.WaitAsync();

                if (OnlyHeader)
                {
                    if (!string.IsNullOrWhiteSpace(_token) &&
                        new List<string> { AuthMethod.OAUTH.ToString(), AuthMethod.JWT_KEY.ToString(), AuthMethod.JWT.ToString(), AuthMethod.SOAP.ToString(), AuthMethod.FRESH_PORTAL_JWT.ToString() }
                    .Contains(_authentication.AuthMethod))
                        _client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _token);

                    else if (!string.IsNullOrWhiteSpace(_token) &&
                        _authentication.AuthMethod == AuthMethod.BASIC_KEY.ToString())
                        _client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Basic", _token);
                }
                else
                {
                    if (_authentication.AuthMethod == AuthMethod.OAUTH.ToString())
                    {
                        var request = new HttpRequestMessage(HttpMethod.Post, _authentication.URL)
                        {
                            Content = new FormUrlEncodedContent(new Dictionary<string, string>
                            {
                                { "client_id", _authentication.Credentials.client_id },
                                { "client_secret", _authentication.Credentials.client_secret },
                                { "grant_type", _authentication.Credentials.grant_type },
                                { "resource", _authentication.Credentials.resource }
                            })
                        };

                        var response = await _client.SendAsync(request);
                        if (response.IsSuccessStatusCode)
                        {
                            var content = await response.Content.ReadAsStringAsync();
                            var payload = JObject.Parse(content);
                            _token = payload.Value<string>("access_token");
                            _tokenExpiration = DateTime.Now.AddSeconds(payload.Value<int>("expires_in")).AddSeconds(-60);
                            _client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _token);
                        }
                        else
                            throw new Exception($"{nameof(GetTokenAsync)} for {AuthMethod.OAUTH} has failed, can't get access token. HttpStatusCode {response.StatusCode} - Error : {response.Content.ReadAsStringAsync()?.Result}");

                    }
                    else if (_authentication.AuthMethod == AuthMethod.BASIC_KEY.ToString())
                    {
                        if (string.IsNullOrWhiteSpace(_authentication.Token))
                            _client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Basic", _token);
                        else if (_authentication.Token != _token)
                        {
                            _token = _authentication.Token;
                            _client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Basic", _token);
                        }
                    }
                    else if (_authentication.AuthMethod == AuthMethod.JWT_KEY.ToString())
                    {
                        if (string.IsNullOrWhiteSpace(_authentication.Token))
                            _client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _token);
                        else if (_authentication.Token != _token)
                        {
                            _token = _authentication.Token;
                            _client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _token);
                        }

                    }
                    else if (_authentication.AuthMethod == AuthMethod.JWT.ToString())
                    {
                        var request = new HttpRequestMessage(HttpMethod.Post, _authentication.URL)
                        {
                            Content = new FormUrlEncodedContent(new Dictionary<string, string>
                            {
                                { "grant_type", _authentication.Credentials?.grant_type },
                                { "username", _authentication.Credentials?.username },
                                { "password", _authentication.Credentials?.password }
                            })
                        };

                        var response = await _client.SendAsync(request);
                        if (response.IsSuccessStatusCode)
                        {
                            var content = await response.Content.ReadAsStringAsync();
                            var payload = JObject.Parse(content);
                            _token = payload.Value<string>("access_token");
                            _tokenExpiration = DateTime.Now.AddSeconds(payload.Value<int>("expires_in")).AddSeconds(-60);
                            _client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _token);
                        }
                        else
                            throw new Exception($"{nameof(GetTokenAsync)} for {AuthMethod.JWT} has failed, can't get access token. HttpStatusCode {response.StatusCode} - Error : {response.Content.ReadAsStringAsync()?.Result}");
                    }
                    else if (_authentication.AuthMethod == AuthMethod.SOAP.ToString())
                    {
                        var request = new HttpRequestMessage(HttpMethod.Post, _authentication.URL);

                        _client.DefaultRequestHeaders.Remove("SOAPAction");
                        _client.DefaultRequestHeaders.Add("SOAPAction", _authentication.Credentials.soapSettings.SoapAction);

                        var xml = string.Format(_soapLoginTemplate, _authentication.Credentials.username, _authentication.Credentials.password);
                        request.Content = new StringContent(xml, Encoding.UTF8, "text/xml");

                        var response = await _client.SendAsync(request);
                        if (response.IsSuccessStatusCode)
                        {
                            _token = await ReadSoapTokenAsync(response);
                            _tokenExpiration = DateTime.Now.AddSeconds(86400).AddSeconds(-60); // magic number, should be extracted from the xml response
                            _client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _token);
                        }
                        else
                            throw new Exception($"{nameof(GetTokenAsync)} for {AuthMethod.SOAP} has failed, can't get access token. HttpStatusCode {response.StatusCode} - Error : {response.Content.ReadAsStringAsync()?.Result}");
                    }
                    else if (_authentication.AuthMethod == AuthMethod.FRESH_PORTAL_JWT.ToString())
                    {
                        PostAuthenticationDTO postAuthenticationDTO = new PostAuthenticationDTO { Type = _authentication.Credentials?.type, Username = _authentication.Credentials?.username };
                        var options = new JsonSerializerOptions
                        {
                            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                        };
                        var request = new HttpRequestMessage(HttpMethod.Post, _authentication.URL)
                        {
                            Content = new StringContent(System.Text.Json.JsonSerializer.Serialize<PostAuthenticationDTO>(postAuthenticationDTO,options), Encoding.UTF8,"application/json")
                        };
                        var b = await request.Content.ReadAsStringAsync();
                        var response = await _client.SendAsync(request);
                        if (response.IsSuccessStatusCode)
                        {
                            var content = await response.Content.ReadAsStringAsync();
                            var payload = JObject.Parse(content);
                            _token = payload.Value<string>("token");
                            _tokenExpiration = DateTime.Now.AddSeconds(payload.Value<int>("expires")).AddSeconds(-60);
                            _client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _token);
                            //_client.DefaultRequestHeaders.Remove("Authorization");
                            //_client.DefaultRequestHeaders.Add("Authorization", "Bearer" + _token);
                        }
                        else
                            throw new Exception($"{nameof(GetTokenAsync)} for {AuthMethod.FRESH_PORTAL_JWT} has failed, can't get access token. HttpStatusCode {response.StatusCode} - Error : {response.Content.ReadAsStringAsync()?.Result}");
                    }
                    else if (_authentication.AuthMethod == AuthMethod.BEARER.ToString())
                    {
                        var request = new HttpRequestMessage(HttpMethod.Post, _authentication.URL)
                        {
                            Content = new FormUrlEncodedContent(new Dictionary<string, string>
                            {
                                { "client_id", _authentication.Credentials.client_id },
                                { "client_secret", _authentication.Credentials.client_secret },
                                { "grant_type", _authentication.Credentials.grant_type },
                                { "audience", _authentication.Credentials.audience }
                            })
                        };

                        var response = await _client.SendAsync(request);
                        if (response.IsSuccessStatusCode)
                        {
                            var content = await response.Content.ReadAsStringAsync();
                            var payload = JObject.Parse(content);
                            _token = payload.Value<string>("access_token");
                            _tokenExpiration = DateTime.Now.AddSeconds(payload.Value<int>("expires_in")).AddSeconds(-60);
                            _client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _token);
                        }
                        else
                            throw new Exception($"{nameof(GetTokenAsync)} for {AuthMethod.BEARER} has failed, can't get access token. HttpStatusCode {response.StatusCode} - Error : {response.Content.ReadAsStringAsync()?.Result}");

                    }
                }
            }
            catch (Exception e)
            {
                _logger?.LogError(e, "Fail to authenticate");
                throw;
            }
            finally
            {
                AccessTokenSemaphore.Release(1);
            }
        }

        private async Task<string> ReadSoapTokenAsync(HttpResponseMessage response)
        {
            var xml = await response.Content.ReadAsStringAsync();
            var xmlDoc = XDocument.Parse(xml);

            var soapResponse = xmlDoc.Descendants()
                .Where(x => x.Name.LocalName == _authentication.Credentials.soapSettings.XMLRootTokenTag)
                    .Select(x => x.Element(x.Name.Namespace + _authentication.Credentials.soapSettings.XMLChildTokenTag))
                    .FirstOrDefault();

            return soapResponse.Value;
        }

        public static async Task<T> ReadSoapDataAsync<T>(HttpResponseMessage response, string XMLRootTag, string XMLChildTag)
        {
            var xml = await response.Content.ReadAsStringAsync();
            var xmlDoc = XDocument.Parse(xml);

            var soapResponse = xmlDoc.Descendants()
                .Where(x => x.Name.LocalName == XMLRootTag)
                    .Select(x => x.Element(x.Name.Namespace + XMLChildTag))
                    .FirstOrDefault();

            return soapResponse is null ? default : soapResponse.Value.To<T>();
        }

        public static string CombineUri(params string?[]? uriParts)
        {
            string uri = string.Empty;
            if (uriParts != null && uriParts.Length > 0)
            {
                char[] trims = new char[] { '\\', '/' };
                uri = (uriParts[0] ?? string.Empty).TrimEnd(trims);
                for (int i = 1; i < uriParts.Length; i++)
                {
                    if (uriParts[i] is null)
                        continue;

                    uri = string.Format("{0}/{1}", uri.TrimEnd(trims), (uriParts[i] ?? string.Empty).TrimStart(trims));
                }
            }
            return uri;
        }

        private static Slice GetSlice(string nextLink)
        {
            var slice = new Slice { Top = 0, Skip = 0 };

            if (nextLink is null)
                return slice;

            // Typically https://interflora-test.sandbox.operations.dynamics.com/data/ProductsV2/?$select=ProductNumber%2CProductDescription%2CProductSearchName&$skip=10000&$top=10000

            // Top
            Regex regexTop = new Regex(@".*top=([^&|\n|\t\s]+)");
            Match matchTop = regexTop.Match(nextLink);
            if (matchTop.Success)
                slice.Top = Convert.ToInt32(matchTop.Groups[1].Value);

            // Skip
            Regex regexSkip = new Regex(@".*skip=([^&|\n|\t\s]+)");
            Match matchSkip = regexSkip.Match(nextLink);
            if (matchSkip.Success)
                slice.Skip = Convert.ToInt32(matchSkip.Groups[1].Value);

            return slice;
        }

        private string XwsseHeaderHandling()
        {
            if (_authentication?.AuthMethod == AuthMethod.XWSSE.ToString())
                return GenerateXwsseHeader();

            return String.Empty;
        }

        private string GenerateXwsseHeader()
        {
            var nonce = GetRandomString(32);
            var timestamp = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ssZ");
            var passwordDigest = Convert.ToBase64String(Encoding.UTF8.GetBytes(Sha1(nonce + timestamp + _authentication.Credentials.password)));
            return String.Format("Username=\"{0}\", PasswordDigest=\"{1}\", Nonce=\"{2}\", Created=\"{3}\"", _authentication.Credentials.username, passwordDigest, nonce, timestamp);
        }

        private static string Sha1(string input)
        {
            var hashInBytes = SHA1.HashData(Encoding.UTF8.GetBytes(input));
            return string.Concat(Array.ConvertAll(hashInBytes, b => b.ToString("x2")));
        }

        private static string GetRandomString(int length)
        {
            var random = new Random();
            string[] chars = new string[] { "0", "1", "2", "3", "4", "5", "6", "8", "9", "a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z" };
            var sb = new StringBuilder();
            for (int i = 0; i < length; i++) sb.Append(chars[random.Next(chars.Length)]);
            return sb.ToString();
        }

        private Uri GetUriForQueryParamAuthMethod(string? endpoint, string? queryParameters)
        {
            var queryParametersToAdd = QueryHelpers.ParseQuery(_authentication.QueryParam);
            var existingQueryParameters = new Dictionary<string, StringValues>();

            if (queryParameters != null)
            {
                existingQueryParameters = QueryHelpers.ParseQuery(queryParameters);
            }

            QueryBuilder queryBuilder = new QueryBuilder();
            foreach (var item in existingQueryParameters)
                queryBuilder.Add(item.Key, (IEnumerable<string>)item.Value);
            foreach (var item in queryParametersToAdd)
                queryBuilder.Add(item.Key, (IEnumerable<string>)item.Value);
            var builder = new UriBuilder(CombineUri(_uri.ToString(), endpoint ?? _endpoint))
            {
                Query = queryBuilder.ToString().Remove(0, 1) // Remove leading "?" mark
            };
            var enrichedUri = builder.Uri;
            return enrichedUri;
        }

        private async Task<HttpResponseMessage> ExecuteWithRetryAsync(Func<Task<HttpResponseMessage>> operation,int maxRetries = 0,int currentRetry = 0)
        {
            HttpResponseMessage response;

            try
            {
                response = await operation(); // Execute the HTTP operation
            }
            catch (HttpRequestException ex)
            {
                // Network-level exceptions (connection issues, DNS failures, etc.)
                if (currentRetry >= maxRetries)
                {
                    _logger.LogError(ex, "HTTP request failed with HttpRequestException after {maxRetries} retries: {message}", maxRetries, ex.ToString());
                    throw;
                }

                // Apply exponential backoff for network errors
                TimeSpan retryDelay = TimeSpan.FromSeconds(Math.Pow(2, currentRetry));
                _logger.LogWarning(ex, "HTTP request failed: {message}. Retrying in {delay} seconds ({retryCount}/{maxRetries})",ex.ToString(), retryDelay.TotalSeconds, currentRetry + 1, maxRetries);

                await Task.Delay(retryDelay);
                return await ExecuteWithRetryAsync(operation, maxRetries, currentRetry + 1);
            }
            catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException || ex.CancellationToken.IsCancellationRequested)
            {
                // Handle timeout or cancellation
                if (currentRetry >= maxRetries)
                {
                    _logger.LogError(ex, "Request timed out or was canceled after {maxRetries} retries: {message}", maxRetries, ex.ToString());
                    throw;
                }

                TimeSpan retryDelay = TimeSpan.FromSeconds(Math.Pow(2, currentRetry));
                _logger.LogWarning(ex, "Request timed out or was canceled: {message}. Retrying in {delay} seconds ({retryCount}/{maxRetries})",ex.ToString(), retryDelay.TotalSeconds, currentRetry + 1, maxRetries);

                await Task.Delay(retryDelay);
                return await ExecuteWithRetryAsync(operation, maxRetries, currentRetry + 1);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected exception during HTTP request operation : {message}", ex.ToString());
                throw; // Re-throw unexpected exceptions
            }

            // Success case - no need for retry
            if (response.IsSuccessStatusCode)
                return response;

            // Don't retry if we've reached max retries
            if (currentRetry >= maxRetries)
            {
                _logger.LogWarning("Received HTTP {statusCode} but Maximum retries ({maxRetries}) reached for HTTP request", response.StatusCode, maxRetries);
                return response;
            }

            // Handle specific status codes for retry
            if (new[] { HttpStatusCode.Unauthorized, HttpStatusCode.Forbidden, HttpStatusCode.Redirect }.Contains(response.StatusCode))
            {
                _logger.LogInformation("Received {statusCode}. Refreshing token and retrying...", response.StatusCode);

                // Refresh token if needed and if authentication is enabled
                if (_authentication != null)
                {
                    try
                    {
                        await TokenHandlingAsync(true);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Exception in TokenHandlingAsync : Failed to refresh token: {message}", ex.ToString());
                        // Continue with retry anyway, in case the token isn't the issue
                    }
                }
                return await ExecuteWithRetryAsync(operation, maxRetries, currentRetry + 1);
            }
            else if (response.StatusCode == HttpStatusCode.TooManyRequests)
            {
                TimeSpan delay;
                try
                {
                    delay = await CalculateRetryDelayFromResponseAsync(response);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Exception calculating retry delay: {message}. Using default delay.", ex.ToString());
                    delay = TimeSpan.FromSeconds(Math.Pow(2, currentRetry) * 5); // Fallback to exponential backoff with base of 5s
                }
                _logger.LogWarning("Rate limit hit. Waiting for {delay} seconds before retry {retryCount}/{maxRetries}",
                    delay.TotalSeconds, currentRetry + 1, maxRetries);

                await Task.Delay(delay);

                // Refresh token if needed and if authentication is enabled
                if (_authentication != null)
                {
                    try
                    {
                        await TokenHandlingAsync();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Exception , Failed to refresh token during rate limit retry: {message}", ex.ToString());
                        // Continue with retry anyway
                    }
                }

                // Recursive retry with increment
                return await ExecuteWithRetryAsync(operation, maxRetries, currentRetry + 1);
            }
            // Server Error side --> retry
            else if ((int)response.StatusCode >= 500)
            {
                // For 50X status codes, apply exponential backoff if retries remain
                if (currentRetry < maxRetries)
                {
                    TimeSpan exponentialDelay = TimeSpan.FromSeconds(Math.Pow(2, currentRetry));
                    _logger.LogWarning("Request failed with {statusCode}. Retrying in {delay} seconds ({retryCount}/{maxRetries})",
                        response.StatusCode, exponentialDelay.TotalSeconds, currentRetry + 1, maxRetries);

                    await Task.Delay(exponentialDelay);

                    // Refresh token if needed and if authentication is enabled
                    if (_authentication != null)
                    {
                        try
                        {
                            await TokenHandlingAsync();
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Exception , Failed to refresh token during retry: {message}", ex.ToString());
                            // Continue with retry anyway
                        }
                    }

                    return await ExecuteWithRetryAsync(operation, maxRetries, currentRetry + 1);
                }
            }

            return response;
        }

        private async Task<TimeSpan> CalculateRetryDelayFromResponseAsync(HttpResponseMessage response)
        {
            // Default delay if we can't determine a better value
            TimeSpan defaultDelay = TimeSpan.FromSeconds(30);

            // Try to get delay from "Retry-After" header (value in seconds)
            if (response.Headers.Contains("Retry-After"))
                if (int.TryParse(response.Headers.GetValues("Retry-After").FirstOrDefault(), out int secondsToWait))
                    return TimeSpan.FromSeconds(Math.Max(1, secondsToWait));

            // Try to get delay from "X-RateLimit-Reset" header (Unix timestamp)
            if (response.Headers.Contains("X-RateLimit-Reset"))
            {
                if (long.TryParse(response.Headers.GetValues("X-RateLimit-Reset").FirstOrDefault(), out long resetTimestamp))
                {
                    DateTimeOffset resetTime = DateTimeOffset.FromUnixTimeSeconds(resetTimestamp);
                    DateTimeOffset now = DateTimeOffset.UtcNow;

                    TimeSpan waitTime = resetTime - now;
                    // Ensure positive wait time of at least 1 second
                    if (waitTime.TotalSeconds > 0)
                        return waitTime;
                    else
                        // If timestamp is in the past, use a small delay
                        return TimeSpan.FromSeconds(1);
                }
            }

            // Fallback to default
            _logger.LogWarning("Could not determine retry delay from response headers. Using default delay of {defaultDelay} seconds",defaultDelay.TotalSeconds);
            return defaultDelay;
        }

        public class Slice
        {
            public int Skip { get; set; }
            public int Top { get; set; }
        }

        class TokenExpiredException : Exception
        {
            public TokenExpiredException()
            {
            }

            public TokenExpiredException(string message)
                : base(message)
            {
            }
        }
        public class PostAuthenticationDTO
        {
            public string Type { get; set; }
            public string Username { get; set; }
        }

    }

}
