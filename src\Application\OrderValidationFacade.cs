﻿using FluentValidation.Results;
using IT.Microservices.CT.Order.Wrapper.Domain.Validator;
using IT.Microservices.CT.Order.Wrapper.Infrastructure;
using IT.Microservices.CT.Order.Wrapper.Infrastructure.Settings;
using IT.Microservices.CT.Order.Wrapper.Presentation.DTO;
using JasperFx.Core;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using ITF.Order.Library.Application;
using ITF.SharedModels.DataModels.Order;
using ITF.SharedLibraries.RAO;
using IT.SharedLibraries.CT.Settings;
using IT.SharedLibraries.CT.CustomAttributes;
using ITF.SharedModels.Group.Enums;
using ITF.SharedLibraries.RAO.DTO;

namespace IT.Microservices.CT.Order.Wrapper.Application;

public interface IOrderValidationFacade
{
    Task<ValidationResponse> ValidateOrder(GlobalOrderModel order, int? numberOfFloristsInResponse);
}

public class OrderValidationFacade(IRAOSupplierHttpService RAOSupplierHttpService,
    IOptionsMonitor<CTOrderWrapperSettings> ctOrderWrapperSettings,
    IOptionsMonitor<CommerceToolCustomSettings> commerceToolsCommonSettings,
    ICTProductSearchService cTProductSearchService,
    IFloristRepository floristRepository) : IOrderValidationFacade
{

    // TODO : if PSM => check if a desciption is provided
    // TODO : check ceremony delay

    public async Task<ValidationResponse> ValidateOrder(GlobalOrderModel order, int? numberOfFloristsInResponse)
    {
        var validator = new GlobalOrderValidator(commerceToolsCommonSettings);
        ValidationResult results = validator.Validate(order);
        if (!results.IsValid)
        {
            string errorMessage = string.Empty;
            results.Errors.ForEach(error => errorMessage += String.Join(", ", error.ErrorMessage));
            return new ValidationResponse {
                IsValide = false,
                ErrorMessage = errorMessage,
                ErrorType = ErrorMessageTypes.VALIDATION_EXCEPTION
            };
        }

        if (ctOrderWrapperSettings?.CurrentValue?.CountryCode.Equals("FR", StringComparison.OrdinalIgnoreCase) ?? false)
        {
            var response = await CheckFrenchOrder(order, numberOfFloristsInResponse);
            if (!response.IsValide)
            {
                return response;
            }
        }

        return new ValidationResponse
        {
            IsValide = true,
            ErrorMessage = string.Empty,
            ErrorType = string.Empty
        };
    }

    private async Task<ValidationResponse> CheckFrenchOrder(GlobalOrderModel order, int? numberOfFloristsInResponse)
    {
        if (order.SenderFloristIdentifier == order.SenderFloristIdentifier)
        {
            return new ValidationResponse
            {
                IsValide = false,
                ErrorMessage = "Sender and executing florist cannot be the same",
                ErrorType = ErrorMessageTypes.VALIDATION_EXCEPTION
            };
        }

        foreach (GlobalOrderProduct product in order.Products)
        {
            bool flowControl = false;
            ValidationResponse value = new();

            (flowControl, value) = OrderValidationUtil.CheckProduct(product, order.Shipping.DeliveryDate, order.Shipping.Moment);
            if (!flowControl)
            {
                return value;
            }

            var ctProduct = await cTProductSearchService.GetProductByKey(product.ProductKey);
            if (ctProduct == null)
            {
                return new ValidationResponse
                {
                    IsValide = false,
                    ErrorMessage = $"Product with key {product.ProductKey} not found",
                    ErrorType = ErrorMessageTypes.VALIDATION_EXCEPTION
                };
            }

            var frenchSettings = ctOrderWrapperSettings.CurrentValue.FrenchSettings;
            var preparationTime = CommerceToolsHelper.GetLongAttribute(CtProductCustomAttributesNames.VariantAttributes.FLORIST_PREPARATION_TIME, ctProduct.MasterVariant.Attributes);

            if (order.Shipping.Moment.Equals(MomentEnum.Morning))
            {
                (flowControl, value) = OrderValidationUtil.CheckDeliveryDate(order.Shipping.DeliveryDate, frenchSettings.MorningLimitHour, frenchSettings.MorningLimitMinutes, preparationTime);
            }
            else
            {
                (flowControl, value) = OrderValidationUtil.CheckDeliveryDate(order.Shipping.DeliveryDate, frenchSettings.AfternoonLimitHour, frenchSettings.AfternoonLimitMinutes, preparationTime);
            }

            if (!flowControl)
            {
                return value;
            }

            (flowControl, value) = OrderValidationUtil.CheckProductPrice(product, frenchSettings.ProductMinPrice, frenchSettings.ProductMaxPrice);
            if (!flowControl)
            {
                return value;
            }

            // TODO get min and max prices from variants and check order price
            //var crPrice = GetCtVariantPrice(product.VariantKey, ctProduct, productQuantity);

        }

        var dto = new FeasabilityOrderDto
        {
            ProductIds = order.Products.Select(p => p.ProductKey).ToList(),
            DeliveryCountry = order.DeliveryCountryCode,
            DeliveryDate = order.Shipping.DeliveryDate,
            DeliveryCity = order.Shipping.City,
            DeliveryStreet = order.Shipping.StreetName,
            DeliveryWindow = OrderValidationUtil.ConvertMomentToRaoDeliveryWindow(order.Shipping.Moment),
            OrderId = order.OrderNumber,
            DeliveryZipCode = order.Shipping.ZipCode,
            TotalAmount = order.OrderTotal ?? 0m
        };

        var raoResponse = await RAOSupplierHttpService.Feasibility(dto, numberOfFloristsInResponse ?? 0);
        if (!raoResponse.IsSuccessStatusCode)
        {
            var content = await raoResponse.Content.ReadAsStringAsync();
            return new ValidationResponse
            {
                IsValide = false,
                ErrorMessage = content,
                ErrorType = ErrorMessageTypes.FEASABILITY_EXCEPTION_RAO_KO
            };
        }

        string responseData = await raoResponse.Content.ReadAsStringAsync();
        var feasabilityResponse = JsonConvert.DeserializeObject<FeasabilityOrderResponseDto>(responseData);
        if (feasabilityResponse == null)
        {
            return new ValidationResponse
            {
                IsValide = false,
                ErrorMessage = "Response from RAO is null or invalid",
                ErrorType = ErrorMessageTypes.FEASABILITY_EXCEPTION_RAO_RESPONSE_DATA_KO
            };
        }

        if (!OrderValidationUtil.IsOrderIsFeasible(feasabilityResponse))
        {
            if (order.CodeAP.IsEmpty())
            {
                return new ValidationResponse
                {
                    IsValide = false,
                    ErrorMessage = feasabilityResponse.FeasibleReason ?? "No feasible florists found",
                    ErrorType = ErrorMessageTypes.FEASABILITY_EXCEPTION_EMPTY_FLORISTS
                };
            }

            var codeApFromDBValid = false;
            var executingFlorist = await floristRepository.GetById(order.ExecutingFloristIdentifier);
            (codeApFromDBValid, var response) = OrderValidationUtil.ValidatePersonalCodeFromDb(order.CodeAP, executingFlorist);

            if (codeApFromDBValid) // code AP not found
            {
                return response;
            }

        }
        else
        {
            // the order is feasible but we have a code AP
            // we use the response from RAO to validate the code AP
            if (!order.CodeAP.IsEmpty())
            {
                var codeApValid = false;
                (codeApValid, var response) = OrderValidationUtil.ValidatePersonalCode(order.CodeAP, order.ExecutingFloristIdentifier, feasabilityResponse.Florists);
                if (!codeApValid)
                {
                    return response;
                }
            }
            else
            {
                // if a executing florist is specified, we check if it exists in the RAO response
                if (!order.ExecutingFloristIdentifier.IsEmpty())
                {
                    var executingFloristFound = false;
                    foreach (FloristDto florist in feasabilityResponse.Florists)
                    {
                        if (florist.FloristId.EqualsIgnoreCase(order.ExecutingFloristIdentifier))
                        {
                            executingFloristFound = true;
                            break;
                        }
                    }
                    if (!executingFloristFound)
                    {
                        return new ValidationResponse
                        {
                            IsValide = false,
                            ErrorMessage = "Executing florist not found in RAO response",
                            ErrorType = ErrorMessageTypes.SELECTED_EXECUTING_FLORIST_NOT_FOUND
                        };
                    }
                }
            }
        }

        return new ValidationResponse
        {
            IsValide = true,
            ErrorMessage = string.Empty,
            ErrorType = string.Empty
        };

    }
}
