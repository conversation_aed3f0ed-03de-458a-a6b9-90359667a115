Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.5.2.0
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IT.Microservices.CT.Order.Wrapper", "src\IT.Microservices.CT.Order.Wrapper.csproj", "{FDFA2A68-600F-3109-2410-ACB5D834F6FE}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{0AB3BF05-4346-4AA6-1389-037BE0695223}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IT.Microservices.CT.Order.Wrapper.UnitTests", "tests\IT.Microservices.CT.Order.Wrapper.UnitTests\IT.Microservices.CT.Order.Wrapper.UnitTests.csproj", "{295DE44B-092C-88AB-7941-2EAD8C3C59A6}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{FDFA2A68-600F-3109-2410-ACB5D834F6FE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FDFA2A68-600F-3109-2410-ACB5D834F6FE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FDFA2A68-600F-3109-2410-ACB5D834F6FE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FDFA2A68-600F-3109-2410-ACB5D834F6FE}.Release|Any CPU.Build.0 = Release|Any CPU
		{295DE44B-092C-88AB-7941-2EAD8C3C59A6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{295DE44B-092C-88AB-7941-2EAD8C3C59A6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{295DE44B-092C-88AB-7941-2EAD8C3C59A6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{295DE44B-092C-88AB-7941-2EAD8C3C59A6}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{295DE44B-092C-88AB-7941-2EAD8C3C59A6} = {0AB3BF05-4346-4AA6-1389-037BE0695223}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {9E5D640C-4ACD-4648-BC37-9E04A640FA46}
	EndGlobalSection
EndGlobal
