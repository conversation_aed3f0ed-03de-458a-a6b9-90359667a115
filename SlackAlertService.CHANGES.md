# SlackAlertService Enhancement Summary

## Overview
Enhanced the SlackAlertService with environment and country filtering capabilities, plus dynamic channel generation based on environment variables.

## New Features

### 1. Environment Filtering
- **Supported Environments**: `dev`, `recette`, `perf`, `preprod`, `prod`, `development`
- **Environment Variable**: `ASPNETCORE_ENVIRONMENT`
- **New Enum**: `AlertEnvironment` with values: `Dev`, `Recette`, `Perf`, `Preprod`, `Prod`, `Development`

### 2. Country Filtering  
- **Supported Countries**: `fr`, `it`, `es`, `pt`, `dk`, `se`
- **Environment Variable**: `ASPNETCORE_COUNTRY`
- **New Enum**: `AlertCountry` with values: `Fr`, `It`, `Es`, `Pt`, `Dk`, `Se`

### 3. Dynamic Channel Generation
Automatically generates channel names when no explicit channel is provided:

**Pattern**: `alerts-ms-{country}-{env}`

**Special Cases**:
- **Production**: `alerts-ms-{country}` (removes `-env` suffix)
- **Development**: `alerts-ms-debug` (ignores country)

**Examples**:
- France + Prod → `alerts-ms-fr`
- Italy + Dev → `alerts-ms-it-dev`
- Any country + Development → `alerts-ms-debug`

### 4. Enhanced AlertLevel Class
```csharp
public class AlertLevel
{
    public HashSet<AlertEnvironment> AllowedEnvironments { get; set; }
    public HashSet<AlertCountry> AllowedCountries { get; set; }
    
    // Multiple constructors for flexibility
    public AlertLevel(params AlertEnvironment[] environments)
    public AlertLevel(IEnumerable<AlertEnvironment> environments)
    public AlertLevel(IEnumerable<AlertEnvironment> environments, IEnumerable<AlertCountry> countries)
    public AlertLevel(AlertEnvironment[] environments, AlertCountry[] countries)
}
```

### 5. Configuration Enhancements
Added to `SlackAlertOptions`:
```csharp
public List<string>? DefaultAllowedEnvironments { get; set; }
public List<string>? DefaultAllowedCountries { get; set; }
```

## Updated Method Signatures

### ISlackAlertService Interface
```csharp
Task SendErrorAlertAsync(string errorMessage, Exception? exception = null, string? channel = null, AlertLevel? alertLevel = null);

Task SendCustomAlertAsync(string title, string message,
    Dictionary<string, string>? fields = null,
    string? color = "danger",
    string? channel = null,
    AlertLevel? alertLevel = null);
```

## Filtering Logic Priority

1. **Method-level AlertLevel** (highest priority)
   - If provided, uses only the environments and countries specified in the AlertLevel
   - If AlertLevel has no countries specified, only checks environment filtering

2. **Configuration-level defaults**
   - Uses `DefaultAllowedEnvironments` and `DefaultAllowedCountries` from appsettings.json
   - Both environment and country must be allowed (AND logic)

3. **Default behavior** (lowest priority)
   - Sends to all environments and countries (original behavior)

## Configuration Example

```json
{
  "SlackAlert": {
    "ApiToken": "xoxb-your-slack-bot-token-here",
    "DefaultChannel": "alerts-ms-fr",
    "DefaultAllowedEnvironments": ["preprod", "prod"],
    "DefaultAllowedCountries": ["fr", "it"],
    // ... other existing configuration
  }
}
```

## Usage Examples

### Basic Usage (uses configuration defaults)
```csharp
await _slackAlertService.SendErrorAlertAsync("Error message", exception);
```

### Environment-only filtering
```csharp
var prodOnly = new AlertLevel(AlertEnvironment.Prod);
await _slackAlertService.SendErrorAlertAsync("Critical error", exception, alertLevel: prodOnly);
```

### Environment + Country filtering
```csharp
var environments = new[] { AlertEnvironment.Preprod, AlertEnvironment.Prod };
var countries = new[] { AlertCountry.Fr, AlertCountry.It };
var alertLevel = new AlertLevel(environments, countries);

await _slackAlertService.SendCustomAlertAsync("Title", "Message", alertLevel: alertLevel);
```

## Backward Compatibility
- ✅ All existing code continues to work without changes
- ✅ New parameters are optional with default values
- ✅ Original behavior maintained when no filtering is configured

## Key Implementation Details

1. **Environment Variable Reading**: Case-insensitive parsing of `ASPNETCORE_ENVIRONMENT` and `ASPNETCORE_COUNTRY`

2. **Error Handling**: Unknown environments/countries default to allowing alerts (fail-safe approach)

3. **Dynamic Channel Fallback**: If channel generation fails, falls back to `DefaultChannel` from configuration

4. **Logging**: Debug-level logging when alerts are filtered out, warning-level for configuration issues

## Files Modified
- `src/Alerting/SlackAlertService.cs` - Main implementation
- `appsettings.example.json` - Configuration example
- `SlackAlertService.Usage.Example.cs` - Usage examples

## Testing Recommendations
1. Test with different combinations of `ASPNETCORE_ENVIRONMENT` and `ASPNETCORE_COUNTRY`
2. Verify dynamic channel generation for all environment/country combinations
3. Test filtering logic with various AlertLevel configurations
4. Verify backward compatibility with existing code
