﻿using ITF.SharedLibraries.ExtensionMethods;
using ITF.SharedLibraries.MongoDB.Repository;
using ITF.SharedModels.DataModels.Florist;
using ITF.SharedModels.Group.Enums;
using MongoDB.Driver;

namespace IT.Microservices.CT.Order.Wrapper.Infrastructure;

public interface IFloristRepository : IMongoRepository<GlobalFloristModel>
{
    Task<GlobalFloristModel> GetById(string floristId);
    Task SaveDocuments(GlobalFloristModel florist);
}

public class FloristRepository(ITF.SharedLibraries.MongoDB.Configuration configuration,
    IMongoClient mongoClient,
    ILogger<FloristRepository> logger) : MongoRepository<GlobalFloristModel>(mongoClient, configuration.DatabaseName, "Florists"), IFloristRepository
{
    public async Task<GlobalFloristModel> GetById(string floristId)
    {
        logger.LogInformation("Process {process} for florist {floristId}", nameof(GetById), floristId);
        // We just need those types of documents when getting a florist
        // to get order documents, use GetOrderDocuments()
        List<DocTypeEnum> docTypesAccepted = new List<DocTypeEnum> { DocTypeEnum.IT_INVOICE_VENDOR, DocTypeEnum.IT_INVOICE_BALANCE, DocTypeEnum.IT_INVOICE_PURCHASE };
        var florist = await FilterOneByAsync(o => o.FloristId == floristId);

        if (florist == null)
        {
            logger.LogInformation("Can't find active Florist with id {id}", floristId);
            return null;
        }


        // If you find a better solution to filter documents, go ahead
        //var documents = florist.Documents.Where(doc => docTypesAccepted.Any(type => doc.DocType == type)).ToList();
        //florist.Documents = documents;

        try
        {
            logger.LogInformation("Florist retrieved {json}", florist != null ? florist.Serialize() : "null");
        }
        catch { }

        return florist;
    }

    public async Task SaveDocuments(GlobalFloristModel florist)
    {
        var updateDef = Builders<GlobalFloristModel>.Update
                      .Set(o => o.Documents, florist.Documents)
                      .Set(o => o.LastUpdate, florist.LastUpdate);

        var filter = Builders<GlobalFloristModel>.Filter.Eq(x => x.Id, florist.FloristId);
        await Collection.UpdateOneAsync(filter, updateDef);
    }
}
