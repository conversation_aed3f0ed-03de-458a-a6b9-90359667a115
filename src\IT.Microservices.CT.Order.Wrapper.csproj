﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <UserSecretsId>02f6ecbb-6e11-43c0-8694-105088e11f24</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerfileContext>..\..\..</DockerfileContext>
    <DockerComposeProjectPath>..\..\..\docker-compose.dcproj</DockerComposeProjectPath>
	  <ImplicitUsings>enable</ImplicitUsings>
	  <Nullable>enable</Nullable>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DocumentationFile>IT.Microservices.CT.Order.Wrapper.xml</DocumentationFile>
    <NoWarn>1701;1702;1591</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Presentation\ProductDTO.cs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="FluentValidation" Version="11.9.0" />
	  <PackageReference Include="ITE.Order.Library" Version="2.19.0" />
	  <PackageReference Include="ITF.Order.Library" Version="2.20.0" />
	  <PackageReference Include="IT.SharedLibraries.CT" Version="2.78.1" />
	  <PackageReference Include="ITF.SharedLibraries" Version="8.31.0" />
	  <PackageReference Include="ITF.SharedModels" Version="8.27.0" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.20.1" />
  </ItemGroup>


  <ItemGroup>
    <ProjectReference Include="..\..\ITF.SharedLibraries\src\ITF.SharedLibraries.csproj" />
	  <ProjectReference Include="..\..\ITF.Order.Library\src\ITF.Order.Library.csproj" />
	  <ProjectReference Include="..\..\ITE.Order.Library\src\ITE.Order.Library.csproj" />
    <ProjectReference Include="..\..\ITF.SharedModels\src\ITF.SharedModels.csproj" />
	  <ProjectReference Include="..\..\IT.SharedLibraries.CT\src\IT.SharedLibraries.CT.csproj" />
	  
  </ItemGroup>
  
</Project>
