﻿using Amazon.Runtime.Internal.Util;
using ITF.SharedLibraries.RAO.DTO;
using JasperFx.Core;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Collections.Specialized;

namespace ITF.SharedLibraries.RAO
{
    public class RAOSupplierHttpService : ITF.SharedLibraries.HttpClient.HttpClient, IRAOSupplierHttpService
    {
        public RAOSupplierHttpService(System.Net.Http.HttpClient httpClient,
            IConfiguration config,
            ILogger<RAOSupplierHttpService> logger
            ) : base(httpClient, config, "RAOEndpoint", logger)
        {
        }
        public async Task<HttpResponseMessage> GetFloristCalendars(string floristId)
        {
            return await GetAsync($"api/Florists/{floristId}/Calendars");
        }
        public async Task<HttpResponseMessage> GetFloristCalendarException(string floristId, string start, string end)
        {
            return await GetAsync($"api/Florists/{floristId}/CalendarExceptions/{start}/{end}");
        }
        public async Task<HttpResponseMessage> AddFloristCalendarException(string floristId, string user, AddFloristCalendarExceptionRAODTO dto)
        {
            return await PutAsync(dto, $"api/Florists/{floristId}/CalendarExceptions/{user}");
        }

        public async Task<HttpResponseMessage> UpdateFloristCalendarException(string floristId, string date, string user, CalendarException dto)
        {
            return await PatchAsync(dto, $"api/Florists/{floristId}/CalendarExceptions/{date}/{user}");
        }

        public async Task<HttpResponseMessage> DeleteFloristCalendarException(string floristId, string date, string user, string calendarExceptionId)
        {
            return await DeleteAsync($"api/Florists/{floristId}/CalendarExceptions/{date}/{user}?calendarExceptionId={calendarExceptionId}");
        }
        public async Task<HttpResponseMessage> UpdateFloristCalendar(string floristId, string dayOfWeek, string user, Calendar dto)
        {
            return await PatchAsync(dto, $"api/Florists/{floristId}/Calendars/{dayOfWeek}/{user}");
        }

        public async Task<HttpResponseMessage> UpdateFloristProductStock(string floristId, string productId, bool hasStock, string source)
        {
            return await PatchAsync($"api/Florists/{floristId}/Stock/{productId}/{hasStock}?source={source}");
        }

        public async Task<HttpResponseMessage> UpdateFloristCodeAP(string floristId,string codeAP, string user)
        {
            return await PatchAsync($"api/Florists/{floristId}/CodeAP/{codeAP}/{user}");
        }

        public async Task<HttpResponseMessage> UpdateFloristEmail(string floristId, string email, string user)
        {
            return await PatchAsync($"api/Florists/{floristId}/email/{email}/{user}");
        }

        public async Task<HttpResponseMessage> UpdateOrderStatus(string orderId, string newStatus, UpdateOrderStatusRAODTO dto)
        {
            return await PatchAsync(dto, $"api/Orders/{orderId}/{newStatus}");
        }

        public async Task<HttpResponseMessage> GetFloristContext(string floristId)
        {
            return await GetAsync($"api/Florists/SynchronizeContext?floristId={floristId}");
        }

        public async Task<HttpResponseMessage> Feasibility(FeasabilityOrderDto order, int maxFloristsInResponse)
        {
            var urlParameters = CreatUrlForFaisability(order, maxFloristsInResponse);
            return await PostAsync(order.ProductIds, $"api/Orders/Feasability?{urlParameters}");
        }

        private static string CreatUrlForFaisability(FeasabilityOrderDto order, int maxFloristsInResponse)
        {
            NameValueCollection queryString = System.Web.HttpUtility.ParseQueryString(string.Empty);

            // on force un 'X' dans le ZipCode pour le forcer dans l'URL car required dans la RAO pour le moment
            queryString.Add("zipCode", !order.DeliveryZipCode.IsEmpty() ? order.DeliveryZipCode : "X");
            queryString.Add("deliveryDate", order.DeliveryDate.ToString("yyyy-MM-dd"));
            queryString.Add("orderAmount", order.TotalAmount.ToString());
            queryString.Add("city", order.DeliveryCity);
            queryString.Add("address", order.DeliveryStreet);
            queryString.Add("deliveryWindow", order.DeliveryWindow);
            // byPassRules = true pour désactiver la règle des 200€ qui répond toujours ok mais sans renvoyer d'executants.
            queryString.Add("byPassRules", "true");
            queryString.Add("OrderId", order.OrderId);
            queryString.Add("countryCode", order.DeliveryCountry);

            if (maxFloristsInResponse > 0)
            {
                queryString.Add("nbFloristsMax", maxFloristsInResponse.ToString());
            }

            return queryString.ToString();
        }
    }

}
