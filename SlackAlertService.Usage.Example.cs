using ITF.SharedLibraries.Alerting;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace ITF.SharedLibraries.Examples;

/// <summary>
/// Example usage of the SlackAlertService with environment and country filtering
/// </summary>
public class SlackAlertServiceUsageExample
{
    private readonly ISlackAlertService _slackAlertService;
    private readonly ILogger<SlackAlertServiceUsageExample> _logger;

    public SlackAlertServiceUsageExample(
        ISlackAlertService slackAlertService,
        ILogger<SlackAlertServiceUsageExample> logger)
    {
        _slackAlertService = slackAlertService;
        _logger = logger;
    }

    /// <summary>
    /// Example 1: Send error alert without filtering (uses default behavior)
    /// This will respect the DefaultAllowedEnvironments and DefaultAllowedCountries configuration if set
    /// Channel will be dynamically generated based on ASPNETCORE_COUNTRY and ASPNETCORE_ENVIRONMENT
    /// </summary>
    public async Task SendBasicErrorAlert()
    {
        try
        {
            // Some operation that might fail
            throw new InvalidOperationException("Something went wrong!");
        }
        catch (Exception ex)
        {
            // This will use default environment/country filtering from configuration
            // Channel will be auto-generated: alerts-ms-{country}-{env} or alerts-ms-{country} for prod
            await _slackAlertService.SendErrorAlertAsync(
                "Critical error occurred in payment processing",
                ex);
        }
    }

    /// <summary>
    /// Example 2: Send error alert only to production environments (all countries)
    /// This overrides any default configuration
    /// </summary>
    public async Task SendProductionOnlyErrorAlert()
    {
        try
        {
            // Some critical operation
            throw new InvalidOperationException("Critical production issue!");
        }
        catch (Exception ex)
        {
            // Only send to production environment, all countries
            var prodOnlyAlert = new AlertLevel(AlertEnvironment.Prod);
            
            await _slackAlertService.SendErrorAlertAsync(
                "CRITICAL: Production system failure detected",
                ex,
                channel: null, // Use dynamic channel generation
                alertLevel: prodOnlyAlert);
        }
    }

    /// <summary>
    /// Example 3: Send alert to specific environments and countries
    /// </summary>
    public async Task SendSpecificEnvironmentAndCountryAlert()
    {
        // Send to preprod and prod, but only for France and Italy
        var environments = new[] { AlertEnvironment.Preprod, AlertEnvironment.Prod };
        var countries = new[] { AlertCountry.Fr, AlertCountry.It };
        var alertLevel = new AlertLevel(environments, countries);
        
        await _slackAlertService.SendCustomAlertAsync(
            "Deployment Notification",
            "New version deployed successfully",
            fields: new Dictionary<string, string>
            {
                { "Version", "1.2.3" },
                { "Deployed By", "CI/CD Pipeline" },
                { "Deployment Time", DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss UTC") }
            },
            color: "good", // Green color
            channel: null, // Use dynamic channel generation
            alertLevel: alertLevel);
    }

    /// <summary>
    /// Example 4: Send alert to development environments only (including Development)
    /// </summary>
    public async Task SendDevelopmentAlert()
    {
        var devEnvironments = new AlertLevel(
            AlertEnvironment.Dev,
            AlertEnvironment.Development,
            AlertEnvironment.Recette);
        
        await _slackAlertService.SendCustomAlertAsync(
            "Development Notice",
            "Database migration completed in development environments",
            fields: new Dictionary<string, string>
            {
                { "Migration", "AddUserPreferences_v2" },
                { "Duration", "45 seconds" }
            },
            color: "warning", // Yellow color
            alertLevel: devEnvironments);
    }

    /// <summary>
    /// Example 5: Send alert with explicit channel (bypasses dynamic generation)
    /// </summary>
    public async Task SendAlertToSpecificChannel()
    {
        var alertLevel = new AlertLevel(AlertEnvironment.Prod);
        
        await _slackAlertService.SendCustomAlertAsync(
            "Custom Channel Alert",
            "This alert goes to a specific channel",
            channel: "alerts-custom-channel", // Explicit channel overrides dynamic generation
            alertLevel: alertLevel);
    }

    /// <summary>
    /// Example 6: Country-only filtering (all environments for specific countries)
    /// </summary>
    public async Task SendCountrySpecificAlert()
    {
        // All environments, but only Nordic countries
        var allEnvironments = Enum.GetValues<AlertEnvironment>();
        var nordicCountries = new[] { AlertCountry.Dk, AlertCountry.Se };
        var alertLevel = new AlertLevel(allEnvironments, nordicCountries);
        
        await _slackAlertService.SendCustomAlertAsync(
            "Nordic Region Alert",
            "Special announcement for Nordic countries",
            alertLevel: alertLevel);
    }
}

/// <summary>
/// Example configuration and service registration
/// </summary>
public static class ServiceRegistrationExample
{
    public static void ConfigureServices(IServiceCollection services, IConfiguration configuration)
    {
        // Register the Slack alert service
        services.AddSlackAlertService(configuration);
        
        // Register your service that uses the alert service
        services.AddScoped<SlackAlertServiceUsageExample>();
    }
}

/*
Dynamic Channel Generation Examples:

Environment Variables:
ASPNETCORE_ENVIRONMENT=prod, ASPNETCORE_COUNTRY=fr → Channel: alerts-ms-fr
ASPNETCORE_ENVIRONMENT=dev, ASPNETCORE_COUNTRY=it → Channel: alerts-ms-it-dev
ASPNETCORE_ENVIRONMENT=development, ASPNETCORE_COUNTRY=fr → Channel: alerts-ms-fr-debug
ASPNETCORE_ENVIRONMENT=preprod, ASPNETCORE_COUNTRY=es → Channel: alerts-ms-es-preprod

Configuration Priority:
1. Method-level AlertLevel parameter (highest priority)
2. Configuration-level DefaultAllowedEnvironments/DefaultAllowedCountries
3. Default behavior (send to all environments/countries)

Supported Environment Values: dev, recette, perf, preprod, prod, development
Supported Country Values: fr, it, es, pt, dk, se
*/
