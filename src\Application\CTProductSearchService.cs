﻿using commercetools.Base.Client;
using commercetools.Sdk.Api.Client.RequestBuilders.ProductProjections;
using commercetools.Sdk.Api.Extensions;
using commercetools.Sdk.Api.Models.Categories;
using commercetools.Sdk.Api.Models.Channels;
using commercetools.Sdk.Api.Models.Common;
using commercetools.Sdk.Api.Models.Products;
using IT.Microservices.CT.Order.Wrapper.Presentation.DTO;
using IT.SharedLibraries.CT.Channels;
using IT.SharedLibraries.CT.CustomAttributes;
using IT.SharedLibraries.CT.ExtensionMethods;
using IT.SharedLibraries.CT.Products;
using IT.SharedLibraries.CT.Settings;
using ITE.Order.Library.Application;
using Microsoft.Extensions.Options;

namespace IT.Microservices.CT.Order.Wrapper.Application;

public interface ICTProductSearchService
{
    Task<List<IProductProjection>> FindProductByKeyOrName(string searchString);
    Task<IProductProjection> GetProductByKey(string productKey);
    Task<List<IProductProjection>> GetAccessories();
    void SetPrice(IProductVariant variant, ProductVariantDTO productVariantDTO);
    Task Init();
    List<ProductSearchDTO> ToProductSearchDTOList(List<IProductProjection> productSearchList);
    ProductSearchDTO ToProductSearchDTO(IProductProjection product);
    Task<List<IProductProjection>> GetAllProducts();
}

public class CTProductSearchService : ICTProductSearchService
{
    private readonly IClient _commerceToolsClient;
    private readonly IConfiguration _configuration;
    private readonly ILogger<CTProductSearchService> _logger;
    private readonly IOptionsMonitor<CommerceToolCustomSettings> _commonSettings;
    private readonly ICategoryService _categoryService;
    private readonly IChannelService _channelService;
    private readonly string _projectKey;
    private readonly string _storeKey;
    private readonly string _channelKey;
    private readonly string _accessoriesCategoryKey;
    private readonly string _productCategoryKey;
    private readonly List<string>? _productTypeCategoryKey;
    private readonly string _localCode;
    private const int SEARCH_LIMIT = 100;
    private const int ACCESSORIES_LIMIT = 500;
    private IChannel? channel = null;
    private ICategory? accessoriesCategory = null;
    private ICategory? productsCategory = null;

    public CTProductSearchService(IClient commerceToolsClient, IConfiguration configuration, ILogger<CTProductSearchService> logger, IOptionsMonitor<CommerceToolCustomSettings> commonSettings, 
        ICategoryService categoryService, IChannelService channelService)
    {
        _commerceToolsClient = commerceToolsClient;
        _configuration = configuration;
        _logger = logger;
        _commonSettings = commonSettings;
        _projectKey = _configuration.GetSection("Client:ProjectKey").Value!;
        _storeKey = _commonSettings.CurrentValue.LocalCountryStoreKey;
        _channelKey = _commonSettings.CurrentValue.LocalCountryChannelKey;
        _accessoriesCategoryKey = _commonSettings.CurrentValue.LocalCountryAccessoriesCategoryKey;
        _productCategoryKey = _commonSettings.CurrentValue.LocalCountryProductsCategoryKey;
        _categoryService = categoryService;
        _channelService = channelService;
        _localCode = _commonSettings.CurrentValue.LocalCountryCode.ToLower();
        _productTypeCategoryKey = _configuration.GetSection("CommerceToolsProductsTypesKey").Get<List<string>>();
    }

    private async Task<ByProjectKeyProductProjectionsSearchGet> AddProductTypesFilter(ByProjectKeyProductProjectionsSearchGet req)
    {
        string? filter = null;
        if(_productTypeCategoryKey?.Count > 0)
        {
            foreach (var productTypeKey in _productTypeCategoryKey)
            {
                var productTypeId = await GetProductTypeIdFromKey(productTypeKey);
                if (productTypeId != null)
                {
                    if (filter == null)
                        filter = $"\"{productTypeId}\"";
                    else
                        filter += $",\"{productTypeId}\"";
                }

            }
        }

        return filter is null ? req : req.WithFilter($"productType.id:{filter}");
    }

    private async Task<string?>GetProductTypeIdFromKey(string productTypeKey)
    {
        try
        {
            var productType = await _commerceToolsClient.WithApi().WithProjectKey(_projectKey).ProductTypes()
                .WithKey(productTypeKey).Get().ExecuteAsync();
            return productType.Id;
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error retrieving product type with key {ProductTypeKey} , exception : {ex}", productTypeKey,e.ToString());
            return null;
        }

    }

    public async Task Init()
    {
        channel = await _channelService.GetByKey(_channelKey);
        // normal case for FR as we dont have category for ACC we just fallback to product type == Accessories
        if (!_commonSettings.CurrentValue.LocalCountryCode.Equals("FR", StringComparison.CurrentCultureIgnoreCase))
        {
            accessoriesCategory = await _categoryService.GetByKey(_accessoriesCategoryKey);
        }
        productsCategory = await _categoryService.GetByKey(_productCategoryKey);

    }

    public async Task<List<IProductProjection>> FindProductByKeyOrName(string searchString)
    {
        if (channel == null)
        {
            throw new InvalidOperationException("Channel is null. Ensure Init() is called before using this method.");
        }

        IProductProjectionPagedSearchResponse ctSearch = await _commerceToolsClient
            .WithApi().WithProjectKey(_projectKey)
            .ProductProjections()
            .Search()
            .Get()
            .WithFuzzy(false)
            .WithStoreProjection(_commonSettings.CurrentValue.LocalCountryStoreKey)
            .WithPriceCurrency("EUR")
            .WithPriceChannel(channel.Id)
            .WithText(_localCode, searchString)
            .WithLimit(SEARCH_LIMIT)
            .ExecuteAsync();

        return [.. ctSearch.Results];
    }

    public async Task<IProductProjection> GetProductByKey(string productKey)
    {
        IProductProjection ctProd = await _commerceToolsClient
            .WithApi().WithProjectKey(_projectKey)
            .ProductProjections()
            .WithKey(productKey)
            .Get()
            .WithExpand("variants[*]")
            .WithStoreProjection(_storeKey)
            .ExecuteAsync();
        
        return ctProd;
    }

    public async Task<List<IProductProjection>> GetAccessories()
    {
        var request = _commerceToolsClient
            .WithApi().WithProjectKey(_projectKey)
            .ProductProjections().Search().Get()
            .WithStoreProjection(_commonSettings.CurrentValue.LocalCountryStoreKey)
            .WithPriceCurrency("EUR")
            .WithPriceChannel(channel!.Id).WithLimit(ACCESSORIES_LIMIT);

        if (accessoriesCategory == null)
        {  // FR case
            request = request
                .WithFilter("variants.attributes.product_type.key:\"ACCESSORIES\"")
                .WithFilter("variants.attributes.delivery_type.key:\"FLORIST\"");
            request = await AddProductTypesFilter(request);
        }
        else // default SEU case
            request = request
                .WithFilter($"categories.id: subtree(\"{accessoriesCategory.Id}\")");

        var response = await request.ExecuteAsync();

        //remove rembundle products
        return [.. response.Results.Where(acc => !acc.Key.StartsWith("REMBUNDLE")).ToList()];
    }


    public async Task<List<IProductProjection>> GetAllProducts()
    {

        if (productsCategory == null)
            throw new InvalidOperationException("ProductsCategory null => call Init() before use this methode.");

        const int limit = 500;
        List<IProductProjection> products = [];
        long nextOffset = 0;

        try
        {
            while (nextOffset >= 0)
            {
                var results = await GetProducts(limit, nextOffset);
                if (results != null)
                {
                    products.AddRange([.. results.Results]);
                    nextOffset = GetNextOffset(limit, results.Offset, results.Total);
                }
            }

            ClearFinalCollection(products);
        }
        catch (Exception nfex)
        {
            _logger.LogError(nfex, "Exception while retrieving products with productProjection search api because of {Ex}", nfex.ToString());
        }
        return products;
    }

    private async Task<IProductProjectionPagedSearchResponse> GetProducts(long limit, long offset)
    {
        var request = _commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                        .ProductProjections()
                        .Search()
                        .Get()
                        .WithLimit(limit)
                        .WithOffset(offset)
                        .WithWithTotal(true)
                        .WithMarkMatchingVariants(true)
                        .WithStoreProjection(_storeKey)
                        .WithPriceCurrency("EUR")
                        .WithPriceChannel(channel!.Id)
                        .WithFilter($"categories.id: subtree(\"{productsCategory!.Id}\")")
                        .WithFilter("variants.attributes.cms_published_on.key:\"" + _storeKey + "\"")
                        .WithFilter("variants.attributes.delivery_type.key:\"FLORIST\"");

        // FR CASE : we need to remove the bundle type and OGF products from the search
        if (_commonSettings.CurrentValue.LocalCountryCode.Equals("FR", StringComparison.CurrentCultureIgnoreCase))
            request = await AddProductTypesFilter(request);

        return await request.ExecuteAsync();
    }

    private static long GetNextOffset(int limit, long currentOffset, long? total)
    {
        if (total == null)
        {
            return -1;
        }

        long currentResultCount = currentOffset + limit;
        if (currentResultCount < total)
        {
            return currentResultCount;
        }
        return -1;
    }

    private void ClearFinalCollection(List<IProductProjection> finalResult)
    {
        if (_commonSettings.CurrentValue != null && _commonSettings.CurrentValue.ProductKeysNotToBeDisplayed != null)
        {
            foreach (string pkey in _commonSettings.CurrentValue.ProductKeysNotToBeDisplayed)
            {
                var productToRemove = finalResult.Find(p => p.Key == pkey);
                if (productToRemove != null)
                    finalResult.Remove(productToRemove);
            }
        }

        var productsToRemove = new List<IProductProjection>();

        foreach (var product in finalResult)
        {
            // remove if master variant is not matching and no other variants are matching the query or set the first matching variant as master
            if (!(product.MasterVariant.IsMatchingVariant ?? true))
            {
                if (product.Variants.Any(v => (v.IsMatchingVariant ?? true)))
                {
                    product.MasterVariant = product.Variants.First(v => v.IsMatchingVariant ?? true);
                }
                else
                {
                    productsToRemove.Add(product);
                    continue;
                }
            }
            else
            {
                // the master variant is matching the query but we remove all the others variants that not match the query (if they are)
                var variantsToRemove = product.Variants
                    .Where(variant => !(variant.IsMatchingVariant ?? true))
                    .ToList();

                foreach (var variant in variantsToRemove)
                {
                    product.Variants.Remove(variant);
                }

            }

            // need to remove because any variants with price for this channel
            if (product.MasterVariant.Prices.Count == 0 && product.Variants.All(v => v.Prices.Count == 0))
            {
                productsToRemove.Add(product);
                continue;
            }
        }

        // After the main loop is complete, remove all the products we marked for removal
        foreach (var product in productsToRemove)
        {
            finalResult.Remove(product);
        }
    }

    public void SetPrice(IProductVariant variant, ProductVariantDTO productVariantDTO)
    {
        if (channel == null)
        {
            throw new InvalidOperationException("Channel is null. Ensure Init() is called before using this method.");
        }

        if (variant != null)
        {
            if (variant.Price != null && variant.Price.Tiers == null)
                productVariantDTO.Price = variant.Price.Value.AmountToDecimal();
            else
                foreach (var price in variant.Prices)
                {
                    var id = price.Channel?.Id;
                    if (id != null && id.Equals(channel.Id))
                        productVariantDTO.Price = price.Value.AmountToDecimal();
                    if (price.Tiers != null)
                        foreach (var priceTiers in price.Tiers)
                        {
                            productVariantDTO.PriceTiers.Add(new PriceTiers
                            {
                                CurrencyCode = priceTiers.Value.CurrencyCode,
                                MinimumQuantity = priceTiers.MinimumQuantity,
                                Price = priceTiers.Value.AmountToDecimal()
                            });
                        }
                }
        }
    }

    public List<ProductSearchDTO> ToProductSearchDTOList(List<IProductProjection> productSearchList)
    {
        List<ProductSearchDTO> productSearchDTOList = [];

        if (productSearchList != null && productSearchList.Count > 0)
        {
            foreach (ProductProjection product in productSearchList.Cast<ProductProjection>())
            {
                var stores = product.MasterVariant.GetCmsPublishedOn();
                var productPriceChannelIds = product.GetPriceChannelIds();
                if (stores != null && stores.Contains(_commonSettings.CurrentValue.LocalCountryStoreKey) && productPriceChannelIds.Contains(channel!.Id))
                {
                    var prodDto = ToProductSearchDTO(product);
                    if (prodDto is not null)
                        productSearchDTOList.Add(prodDto);
                }
            }
        }
        return productSearchDTOList;
    }

    public ProductSearchDTO? ToProductSearchDTO(IProductProjection product)
    {
        List<ProductVariantDTO> variantDTOs = [];

        if (product == null)
            return null;

        List<IProductVariant> variants = [];

        if(product.MasterVariant.Price != null 
            && ((product.MasterVariant.Price.ValidFrom == null) || (product.MasterVariant.Price.ValidFrom >= DateTime.Now)) 
            && ((product.MasterVariant.Price.ValidUntil == null) || (product.MasterVariant.Price.ValidUntil <= DateTime.Now)))

            variants.Add(product.MasterVariant);

        else if (product.MasterVariant.Prices != null
            && product.MasterVariant.Prices.Any(p => p.Channel != null && p.Channel.Id == channel!.Id
                && ((p.ValidFrom == null) || (p.ValidFrom <= DateTime.Now))
                && ((p.ValidUntil == null) || (p.ValidUntil >= DateTime.Now))
                ))

            variants.Add(product.MasterVariant);

        if (product.Variants != null)
        {
            foreach ( var variant in product.Variants)
            {
                if (variant.Price != null
                    && ((variant.Price.ValidFrom == null) || (variant.Price.ValidFrom >= DateTime.Now))
                    && ((variant.Price.ValidUntil == null) || (variant.Price.ValidUntil <= DateTime.Now)))

                    variants.Add(variant);

                else if (variant.Prices != null
                    && variant.Prices.Any(p => p.Channel != null && p.Channel.Id == channel!.Id
                        && ((p.ValidFrom == null) || (p.ValidFrom <= DateTime.Now))
                        && ((p.ValidUntil == null) || (p.ValidUntil >= DateTime.Now))
                    ))

                    variants.Add(variant);
            }
        }

        foreach (var variant in variants)
        {
            ProductVariantDTO productVariantDTO = new(
                    variant.Key,
                    product.Key,
                    CommerceToolsHelper.GetSize(variant.Attributes, _localCode),
                    OrderBuilderServiceBase.GetImage(variant.Assets),
                    CommerceToolsHelper.GetColor(variant.Attributes),
                    CommerceToolsHelper.GetQuantity(variant.Attributes, CtProductCustomAttributesNames.VariantAttributes.MIN_QUANTITY),
                    CommerceToolsHelper.GetQuantity(variant.Attributes, CtProductCustomAttributesNames.VariantAttributes.MAX_QUANTITY));
                
            SetPrice(variant, productVariantDTO);
            variantDTOs.Add(productVariantDTO);
        }

        var productName = "";

        if (product.Name.TryGetValue(_localCode, out string? value))
            productName = value;

        if(variantDTOs.Count >  0)
        {
            return new ProductSearchDTO(
            product.Key,
            productName,
            CommerceToolsHelper.GetBooleanAttribute(CtProductCustomAttributesNames.ALLOW_PRICE_DIFFERENT_FROM_VARIANTS, product.MasterVariant.Attributes),
            [.. variantDTOs.OrderBy(v => v.VariantKey).ThenBy(v => v.Price)]);
        }

        _logger.LogWarning("No variants found for product with key {ProductKey}", product.Key);
        return null;


    }
}
