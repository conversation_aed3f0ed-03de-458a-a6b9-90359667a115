﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
	<ImplicitUsings>enable</ImplicitUsings>
	<Nullable>enable</Nullable>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <Description>
		Interflora .net common libraries
		v 8.29.1 : forgot to push the project file
		v 8.29.0 : Added enum BEARER so as not to modify other existing types
		v 8.28.0 : Add French Feasibility call in RAOSupplierHttpService
		v 8.27.0 : Add default Enabled property to true on the SlackAlertService + increse the default message size to 15000 characters
		v 8.26.1 : Update Lib.Common to 5.4.1 (for real)
		v 8.26.0 : Add SlackAlertService to send alerts to Slack
		v 8.25.1 : Update Lib.Common to 5.4.1
		v 8.25.0 : Update Lib.Common to 5.4.0
		v 8.24.4 : Add Delete http method to httpClient
		v 8.24.3 : Another fix for Upsert contact
		v 8.24.2 : Possible fix for handling Upsert Contact response
		v 8.24.1 : Added a log for CreateOrUpdateContactsAsync
		v 8.24.0 : Attempt to fix the parse issue for batch CreateOrUpdateContact
		v 8.23.0 : Fix CalendarHistory nullable fields and update Stock RAO Dto
		v 8.22.0 : Changed the CriticalBackgroundService to permit several instances
		v 8.21.0 : Add Rao endpoint get update calendar
		v 8.20.0 : fix issue in ListAllContactFieldsId by removing empty string in the list
		v 8.19.2 : Update on Optin for Denmark on OptinSms and OptinNewsletter
		v 8.19.1 : little logs fix on HttpClient
		v 8.19.0 : Rework HttpClient for handling retry 429 response
		v 8.18.0 : adjust Rao DTO hierarchy
		v 8.17.0 : Added new batch methods for getting and creating/updating contacts on Emarsys
		v 8.16.0 : Add OptInSms and OptInNewsletter to OrderEmail and fixed OptInOrigin getting changed every time
		v 8.15.1 : Add RAO route update
		v 8.15.0 : Add RAO http supplier for order and florist update
		v 8.14.0 : Handle Azure username before throw not found user exception
		v 8.13.2 : GSEMIG-214 - Patch
		v 8.13.1 : GSEMIG-214 - Update ITF.Lib.Common ref to 5.3.0 - Patch version
		v 8.13.0 : GSEMIG-214 - Update ITF.Lib.Common ref to 5.3.0
		v 8.12.0 : Add new DK country in Emarsys Library
		v 8.11.0 : Updated SwaggerExtensions.AddSwagger with new DocumentFilter and improved OperationFilter with feature management
		v 8.10.0 : Add Sinch service
		v 8.9.2  : Add missing setting for cancelled order event
		v 8.9.1  : add some missing settings for emarsys event_id
		v 8.9.0  : Added Iflora Messages and DTO for Emarsys Emails
		v 8.8.0  : Added Order cancelled Email event
		v 8.7.0  : Updated ITF.Lib.Common reference to 5.2.1
		v 8.6.2  : Remove logs in keycloak Service for client informations
		v 8.6.1  : Add logs in keycloak Service for client informations
		v 8.6.0  : Add logs in keycloak Service for client informations
		v 8.5.0  : Added Order rescheduled Email event
		v 8.4.0  : Add logs in keycloak Service for client informations and get User result
		v 8.3.0  : fix keycloak requests
		v 8.2.0  : improve logging for polly and reduce retry for default param
		v 8.1.0  : Update ITF.Lib.Common ref to 5.1.0
		v 8.0.2  : Rollback Googleapi lib to V4 - IMPORTANT-
		v 8.0.1  : Rollback MongoDb Version to V2
		v 8.0.0  : Update to .NET 8 / Update all libs
		v 7.46.6 : Adapt PostAsync Uri in order to have dynamic endPoint not provided in config
		v 7.46.5 : Fix AccountDeleted Event in Emarsys Library
		v 7.46.4 : Fix Delivery Reasurance in Emarsys Library
		v 7.46.3 : Add Delivery Reasurance, Order preparation in Emarsys Library
		v 7.46.2 : Add Account Deletion Notification in Emarsys Library
		v 7.46.1 : Add new Email message in Emarsys Library
		v 7.46.0 : Add Absent Recipient Email model in Emarsys Library
		v 7.45.7 : Fix checks on null values on Emarsys Library
		v 7.45.6 : Fix OptIn check on Emarsys Library
		v 7.45.5 : Fix France Country on Emarsys Library
		v 7.45.4 : Changed output of ValidateRegion
		v 7.45.3 : Fix Customer type in Emarsys Library
		v 7.45.2 : Remove unecessary log on get token autentication
		v 7.45.1 : Add more log on get token autentication
		v 7.45.0 : Updated method declaration HandleMessage in ITF.SharedLibraries.AzureServiceBus.Subscriber.IMessageHandler
		v 7.44.2 : Fix Origin consent in Emarsys Library
		v 7.44.1 : Change Consent management for Emarsys contacts
		v 7.44.0 : little refacto kafka BackgroundService + Adapt AzureServiceBus to use CriticalBackgroundService same as kafka
		v 7.43.0 : remove the creation of the error topic in kafka
		v 7.42.0 : Added QUERY_PARAM Auth method for swedish endpoints
		v 7.41.1 : Fix payload on Emarsys Library
		v 7.41.0 : Add a first implementation of an alerting system with kafka
		v 7.40.1 : Add fields on payload entities in Emarsys Library
		v 7.40.0 : Upade deps
		v 7.39.0 : Use options serializer camel case for freshPortal http request
		v 7.38.0 : Update CT SDK to latest Version
		v 7.37.0 : Add Unhealthy state + throw on KafkaException for CommitOffsetOperation from KafkaSubscriber process to kill the app
		v 7.36.0 : Add Unhealthy state + throw on ConsumeException from KafkaSubscriber process to kill the app
		v 7.35.0 : Add basic authentication configuration for Keycloak
		v 7.34.1 : Fixed Json conversion in Emarsys Library
		v 7.34.0 : Fixed Kafka topic creation when using MaxMessageBytes settings
		v 7.33.2 : Fixed Object initialization in Emarsys Library
		v 7.33.1 : Add special Optin management for Italy in Emarsys Library
		v 7.33.0 : Fixed dependencies with vuln - update packages in nuget
		v 7.32.4 : Fixed Multiple topics on one reactor
		v 7.32.3 : Fixed Null Exception on logger for HttpEmarsysService
		v 7.32.2 : Change logs on Handler integrity checks
		v 7.32.1 : Add Serialization Options parameter in Post Async method in HTTPClient Library and fixes on Emarsys Library
		v 7.32.0 : Added XWSSE Auth method for Emarsys and Emarsys Library for contact management and sending emails
		v 7.31.0 : Fix service.name to allow view the service name into apm transac into kibana
		v 7.30.0 : Added ITF.SharedLibraries.Cryptography.AesEncryption
		v 7.29.0 : Add GetAsyncString into HttpClient usefull method helper
		v 7.28.0 : Change AzureServiceBus Consumer impl
		v 7.27.0 : Change GetString To GetStringOrThrow + add a GetString that return null instead of exception into EnvironmentVariable.cs
		v 7.26.0 : Change HttpClient Extension from AddHttpClientWithPolicy with AddHttpClientWithDefaultPolicy To remove confusion with AddHttpClientWithPolicy from Polly HttpClient Extension
		v 7.25.0 : Update BackgroundService Impl for Kafka
		v 7.24.0 : Update HostBuilder Extension Method + Logging Extension
		v 7.23.2 : Fix Add a Check for GoogleMapsSettings not null and set the app UnHealthy into the AddGoogleMapsGeocodingService extension method
		v 7.23.1 : Fix Add a Check for GoogleMapsSettings not null and set the app UnHealthy into the UseGoogleMapsGeocodingService extension method
		v 7.23.0 : Add a Check for GoogleMapsSettings not null and set the app UnHealthy into the UseGoogleMapsGeocodingService extension method
		v 7.22.0 : Add a basic extention for Keyclok Authentication
		v 7.21.0 : Add Kafka settings for Max Messages Bytes in the producer config + at the topic creation
		v 7.20.2 : Some other fix about keycloak service
		v 7.20.1 : Some fix about keycloak service
		v 7.20.0 : Add keycloak service
		v 7.19.0 : upgrade lib.common version
		v 7.18.1 : Add new getAsync method with header param
		v 7.18.0 : Add getAsync contentType header param
		v 7.17.1 : Update System.Data.SqlClient from 4.8.5 to 4.8.6 (vulnerability fix)
		v 7.17.0 : Update reference MongoDB.Driver to 2.23.1
		v 7.16.0 : Fix Kafka consume message error when introduced new message
		v 7.15.0 : Deleted ITF.SharedLibraries.CommerceTools.CtOrderCustomAttributesNames (moved to IT.SharedLibraries.CT.CustomAttributes.CtOrderCustomAttributesNames)
		v 7.14.0 : Update reference
		v 7.13.0 : Added new values "comments" to /CommerceTools/CtOrderCustomAttributesNames
		v 7.12.0 : Added new values to /CommerceTools/CtOrderCustomAttributesNames
		v 7.11.0 : Added /CommerceTools/CtOrderCustomAttributesNames
		v 7.10.0 : Fix removediatrics to be in RAO legacy mode
		v 7.9.1 : Fix Kafka new params
		v 7.9.0 : Add Kafka new params
		v 7.8.0 : Update dependencies
		v 7.7.1 : Remove StopApplication Call in the ASB BackgroundWorker
		v 7.7.0 : Added configuration for http cors
		v 7.6.0 : Update dep : lib.common to use the Error class and the FunctionalExtension package
		v 7.5.0 : Add an SetUnhealthy method when we have exception in ASB to kill the app via AKS
		v 7.4.0 : Add the contentType param for httpClient in post patch and put action
		v 7.3.2 : Fix multi http client conf error in jwt token send part 2
		v 7.3.1 : Fix multi http client conf error in jwt token send
		v 7.3.0 : Add PatchAsync and PutAsync methods into generic HttpClient service with object into Body support
		v 7.2.0 : Add custom Marten projections for event sourced systems
		v 7.1.1 : Remove MediatR.Extensions.Microsoft.DependencyInjection dependency
		v 7.1.0 : Update dependencies
		v 7.0.0 : Target .net 7
		v 6.52.3 : HttpClient : retry after http 429 status error on Count operations as well
		v 6.52.2 : Kafka : remove unhealthyness on commit exception
		v 6.52.1 : HttpClient : retry after http 429 status error
		v 6.52.0 : HttpClient new throughput settings + wait on Retry-After header / Kafka unhealthyness on exception
		v 6.51.0 : Added new HttpClient extension AddHttpClientWithPolicy without retry policy
		v 6.50.0 : fix geocoding precision with adress
		v 6.49.0 : Add new health control behavior
		v 6.48.0 : Remove default logger, set Serilog as default with APM transaction span
		v 6.47.0 : Add APM and Serilog at Program.cs level with workaround
		v 6.46.1 : Rollback APM and Serilog at Program.cs level =&gt; implicit transaction are null
      v 6.46.0 : Add APM and Serilog at Program.cs level
      v 6.45.1 : Fix date reach algo / update dependencies
      v 6.45.0 : Add MongoDB default datetime serializer to localtime
      v 6.44.0 : Update dependencies
      v 6.43.0 : Update dependencies
      v 6.42.4-dev : Serilog alpha version for settings
      v 6.42.3 : Serilog downgrade to last versions
      v 6.42.2 : Serilog upgrade to last versions
      v 6.42.1 : Serilog rollback versions
      v 6.42.0 : Make Unleash not triggered if we stick with in memory feature manager / Geocode with possible missing street / Update dependencies
      v 6.41.0 : Add Marten support for ES / Update Unleash configuration
      v 6.40.1 : Kafka : fix potential deserializer error because of a non matching version in the assembly / enhance log errors
      v 6.40.0 : Add optional conf for kafka / add retentionBytes conf
      v 6.39.1 : Remove logger from date time wrapper methods, inject it instead
      v 6.39.0 : Downgrade CT lib (Breaking changes in payment interface) / remove Adobe Campaign
      v 6.38.0 : Adobe Campaign : new implementation / update Httpclient with Soap / Update dependencies
      v 6.37.0 : Timeconverter : add utc helper
      v 6.36.0 : CommerceTools : update dependencies
      v 6.35.0 : Polly : add extension methods for named policies / update dependencies
      v 6.34.1 : GoogleMaps : fix regression
      v 6.34.0 : MongoDb : add optional settings / update dependencies
      v 6.33.0 : Swagger : display authorizations (roles, policies ...) in action description if any
      v 6.32.0 : Swagger : display direct ITF lib dependencies / rollback APM 1.16 to 1.15 (Redis bug)
      v 6.31.1 : Swagger : remove tag limitations
      v 6.31.0 : APM : add Mongodb instrumentations / refactors / update dependencies
      v 6.30.0 : APM : refactor error transactions
      v 6.29.0 : HttpClient : add Put method
      v 6.28.1 : Logger fallback : fix potential null exception log
      v 6.28.0 : Provide Text.Json PascalCase configuration as part of MVC pipeline
      v 6.27.1 : Logger : remove the whole message serializer in fallback that may cause exception on ptr serializing
      v 6.27.0 : HttpClient : replace Newtonsoft with Text.Json as new standard serializer/deserializer / update dependencies
      v 6.26.0 : Redis : add Elastic APM package and tracing logs
      v 6.25.0 : MongoDB : change skip limit to use facets for best perf
      v 6.24.0 : EventstoreDb : provide projection tools
      v 6.23.0 : Update commercetools dependencies
      v 6.22.0 : EventstoreDb : rework the new grpc client + helpers / Serializer : use Text.Json as the standard for Kafka, Redis and objects / httpclient : throw on bad authentication / update dependencies
      v 6.21.0 : Kafka producer timeout conf opti
      v 6.20.0 : Kafka producer optimization / ASB opti
      v 6.19.0 : Kafka consumer : allow to configure timeout
      v 6.18.0 : Kafka consumer : allow to use default parameters
      v 6.17.0 : Add Keycloack authentication
      v 6.16.0 : Swagger : improve render performance / Httpclient : prevent null logger to throw
      v 6.15.0 : Swagger : add bearer authentication
      v 6.14.0 : Unleash : set feature flags settings non mandatory
      v 6.13.0 : Swagger : allow to provide prefix route (useful for routing operations across country)
      v 6.12.0 : Kafka : publisher and subscriber use now headers for ClrType / upadte dependencies
      v 6.11.0 : Kafka subscriber refactor + add integration tests / refactor unleash / fix logger serializer / update dependencies
      v 6.10.0 : Update dependencies
      v 6.9.0 : HttpClient : throw if http status code is unsuccess instead of trying to read/cast the stream
      v 6.8.0 : HttpClient : use extension method for serialize/deserialize for postAsync
      v 6.7.0 : HttpClient/redis : use extension method for serialize/deserialize
      v 6.6.0 : HttpClient : fix token reusable
      v 6.5.1 : Redis : fix wrong usage of revocation
      v 6.5.0 : Redis : add cache client
      v 6.4.0 : Mongo : add for sake of sample the way to hold a tcp connection alive / Add warmup extensions / update dependencies
      v 6.3.0 : Swagger : remove validator check / update dependencies
      v 6.2.0 : Little change on azureservicebus
      v 6.1.0 : Continue AzureService Bus impl
      v 6.0.1 : Fix Warmup for generic Mongo repository
      v 6.0.0 : [BREAKING CHANGES] MongoDB : remove scoped injected transaction / Warmup : allow to pass a delagate function / Swagger : fix the case where env var is empty
      v 5.7.0 : Add Swagger versioning / update dependencies
      v 5.6.0 : Add commercetools serializer and implement it in Kafka subscriber / Add Azure Service Bus implementation
      v 5.5.0 : Polly : Add exception predicates / Fix empty json config file
      v 5.4.1 : FeatureFlags : fix useless injected configuration
      v 5.4.0 : FeatureFlags : change implementation
      v 5.3.0 : FeatureFlags : add façade class + Unleash implementation / update dependencies / minor fixes
      v 5.2.0 : Kafka : throw on missing topic exception is now settable
      v 5.1.0 : MongoDB : refactor some Find queries in async mode / update dependencies
      v 5.0.1 : Fix Kafka consumer trouble (strange behavior different between workstations)
      v 5.0.0 : Target .net 6
      v 4.14.1 : Fix missing correlationId and causation Id when publishing on a Kafka method
      v 4.14.0 : Kafka : remove publish string prototype / Auto set correlationId and causation Id when publishing / update dependencies
      v 4.13.0 : APM : add helpers / add deep equals helpers / update dep
      v 4.12.0 : Mongo : add insert in replace methos in repo generic
      v 4.11.0 : Kafka : throw on unmet requirement settings / add kafka producer topic in settings
      v 4.10.0 : Update mongo lib to handle settings of database in repo
      v 4.9.0 : Update dependencies (common lib)
      v 4.8.0 : Kafka : use MessagePack for consumers
      v 4.7.0 : Kafka : add retentionMs param / add list comparison extension method
      v 4.6.1 : Kafka : add check if topic exist before create the consumer
      v 4.6.0 : Kafka : change default serializer / change some namespaces
      v 4.5.2 : fix remove config kafka max.poll.intervall.ms to default
      v 4.5.1 : fix Kakfa subscibers with handlers at null / remove log when topic already exist
      v 4.5.0 : Add Kakfa parameters and serializers / Add Polly sync policy
      v 4.4.0 : Add new split method for lists
      v 4.3.0 : Add MumurHash2 algorithm / Kafka : remove autokill from kafka consumer (driven by caller now)
      v 4.2.0 : Elasticsearch : enforce generic rules for UntypedRepository
      v 4.1.0 : Elasticsearch : create base class for POCO / ES : add extension methods for DDD/CQRS aggregate handling / MongoDB : aggregate repository
      v 4.0.0 : BREAKING CHANGES : reshape Mongo repo / change Elk mappings handling / rename DDD to Event Sourcing
      v 3.4.3 : Add HttpClient method To GetAsync specific Object direclty
      v 3.4.2 : Add JWT auth case for HttpClient Helper (for GFS API)
      v 3.4.1 : Add more information in retry callback / Add APM tag extension methods / Wrap exception checks and throwing in Elk repositories
      v 3.4.0 : Add Elk mapping extension method / Allow Kafka replaying events from date / Update dependencies
      v 3.3.0 : Add Elk mapping extension method / Add case in Serialize extension method / Update dependencies
      v 3.2.0 : Add APM tag extension methods / Provide Kafka default handling error methods / Update dependencies
      v 3.1.0 : Add APM tag extension methods / Enhance Kafka subscriber behavior / change json directory observed
      v 3.0.1 : Fix already interpolated template name for Elk index / Update dependencies
      v 3.0.0 : [BREAKING CHANGES] use external lib for base class
      v 2.7.0 : Kafka update CheckIntegrity method / Azure Blob Storage refactor / Update dependencies
      v 2.6.0 : Update dependencies
      v 2.5.0 : Add singletons WarmUp extension method
      v 2.4.0 : Add Azure blob storage repository / update dependencies
      v 2.3.0 : Elk Logging new index / Add Azure Blob Storage repository / Httpclient new methods / update dependencies
      v 2.2.0 : Add GoogleMaps geocoding / TimeConverter
      v 2.1.0 : Add APM tag transaction helper / update dependencies
      v 2.0.0 : Breaking changes : Elasticsearch no more mappings in ctor, throw wrapped exceptions / Remove useless policy in httpclient / update dependencies
      v 1.3.0 : Http : add new extension methods / Update dependencies
      v 1.2.0 : Http : release HttpResponse / Elk : change singleton register method
      v 1.1.0 : Use new semantic versioning
      v 1.0.104 : Update dependencies (shared models)
      v 1.0.103 : Kafka batch publish need provided key / Update dependencies (shared models)
      v 1.0.102 : Allow to create 'on the fly' GroupId for Kafka consumers / update dependencies
      v 1.0.101 : Add new Redis method extension
      v 1.0.100 : Allow to remove ACR from a extension method / update dependencies
      v 1.0.99 : Add UseNewtonsoft for controllers
      v 1.0.98 : Add Json Helper / Add methods Update in Elastic repo / Create a PartialUpdate repo
      v 1.0.97 : Add application lifetime ending on Kafka stop listening / add new method in Elasticsearch repo / update dependencies
      v 1.0.96 : Add http client settings and extension method / Fix time method error / Update dependencies
      v 1.0.95 : Update dependencies
      v 1.0.94 : Update logger failure callback / minor changes
      v 1.0.93 : Add more options in logger
      v 1.0.92 : Add FailureSink in logger
      v 1.0.91 : Add logger fallback logs
      v 1.0.90 : Update ITF.SharedModels dependency
      v 1.0.89 : Update dependencies / Add kafka auto create topics / Add slice in Elk repo method
      v 1.0.88 : Minor changes on ElasticSearch repository
      v 1.0.87 : Refactor logs / Update dependencies / new Kafka consumer thread like method / Break Kafka consuming from route for updates
      v 1.0.86 : Httpclient : allow to specify the odata slice
      v 1.0.85 : Add Elasticsearch methods / update readyness registration / update dependencies
      v 1.0.84 : Add extension methods for Healthchecks and Swagger/ReDoc
      v 1.0.83 : Upgrade to Upgrade to Net 5.0 / upgrade dependencies / Create Polly factory
      v 1.0.82 : Refactor log in HttpClient
      v 1.0.81 : Add Elastic APM / Set logger compatible / Code refactor / update nuget packages
      v 1.0.80 : Handle Dynamics365 token expiration / Update polly extension methods
      v 1.0.79 : Update Http Auth methods
      v 1.0.78 : HttpClient : change header setting, add odata methods / Add extensions methods / Elastic : add missing await operator, remove optional index param, add new ctor / Kafka : add batch producer, add producer new config param / Update ITF.SharedModels dependency
      v 1.0.77 : Update sharedModels lib
      v 1.0.76 : Add authMethod for HttpClient Service
      v 1.0.75 : Add dependency injection nuget packet
      v 1.0.74 : Add methods for elastic repo
      v 1.0.73 : Add catch for kafka global handler
      v 1.0.72 : Add new method to Elk repo / Add new Odata with slice method / Add interface to httpClient / Serilog use appsettings for config / Add new HostBuilder (get rid of Vault)
      v 1.0.71 : Encapsulate configuration when using HttpClient
      v 1.0.70 : Add Readyness route extension method / On Elk repository throw an exception on failure
      v 1.0.69 : Update Shared Model dependency
      v 1.0.68 : Implement 'typed object' in Kafka to use Matching Pattern in handlers
      v 1.0.67 : Update Elasticsearch repository / remove useless Logger / remove middleware from metrics extension methods
      v 1.0.66 : Remove Eventgrid / Add scoped log for Jaeger / Add AppMetrics with Prometheus
      v 1.0.65 : Add healthcheck for Elk and Kafka / Add Jaeger and OpenTracing for distributed traces and logs
      v 1.0.64 : Remove shared models to dedicated lib / Add ElasticSearch support
      v 1.0.63 : New project folder architecture
      v 1.0.62 : Add await on error handling of Kafka multiple subscriber / remove autogenerated elements
      v 1.0.61 : Remove auto generated assemby info
      v 1.0.60 : Add Kafka Producer and Consumer infrastucture part
      v 1.0.59 : Add healthchecks and metrics / Simplify Hashicorp Vault builder
      v 1.0.58 : Implement postgres repository with EF Core / Add Projection for Postgres
      v 1.0.57 : Add Projection from EventStore infrastructure
      v 1.0.56 : Little fix repo postgres
      v 1.0.55 : Add GenericRepo Postgres + Dapper
      v 1.0.54 : Add Domain Driven Design infrastructure part
      v 1.0.53 : Update MongoRepo
      v 1.0.52 : Allow multiple instance register of RabbitMQ handlers
      v 1.0.51 : RabbitMQ minor change for Adding handler separately
      v 1.0.50 : Change RabbitMQ supplier registration + check integrity
      v 1.0.49 : RabbitMQ new extension method / configuration and sub association made by ClassName
      v 1.0.48 : Add error handling for RabbitMq
      v 1.0.47 : Change RabbitMQ subscribers declaration / minor changes in Hashicorp vault
      v 1.0.46 : Implementation of Hashicorp Vault configuration provider with hotreload / FeatureManagement / LogLevel
      v 1.0.45 : Update MongoRepo
      v 1.0.44 : Add method into MongoRepo
      v 1.0.43 : Change ToList into MongoDbRepo
      v 1.0.42 : Add OrderMessageType Enum and change nullable ExpiryTime on BaseMessage
      v 1.0.41 : Add some enums and models for message RabbitMq
      v 1.0.40 : RabbitMQ new subscribing implementation
      v 1.0.39 : Add first implementation of MongoDb
      v 1.0.38 : Little change RabbitMQ setup
      v 1.0.37 : Add RabbitMQ consumers
      v 1.0.36 : Add RabbitMQ client
      v 1.0.35 : Hashicorp vault client serialize dictionnary / Add logger factory for ELK and Seq
      v 1.0.34 : HttpClient class has no more generic ctor, only genric methods / add support for Hashicorp Vault
      v 1.0.33 : Simplify HttpClient class
      v 1.0.32 : Redis client should now be used as a singleton / log by level
      v 1.0.31 : Add BatchGuid for Notif
      v 1.0.30 : Add batches for enumerables
      v 1.0.29 : Throw exceptions on bad token for Adobe token client
      v 1.0.28 : Increase log level for Adobe token client
      v 1.0.27 : Change properties naming
      v 1.0.26 : Add Missing interface for Adobe client
      v 1.0.25 : Add MissingRecipient template for SOAP Adobe call
      v 1.0.24 : Add DeleteAll method for Azure Table
      v 1.0.23 : Suppress 404 cases in Polly Policy
      v 1.0.22 : Change HttpClient timeout / log error in Adobe Client
      v 1.0.21 : Adobe client now log exceptions
      v 1.0.20 : NotifDto is shared in lib
      v 1.0.19 : Add shared objects and enum
      v 1.0.18 : Adobe Campaign fix missing method in interface
      v 1.0.17 : Adobe Campaign configuration objects
      v 1.0.16 : Add Adobe Campaign client helper
      v 1.0.15 : Create logger from injected factory
      v 1.0.14 : Redis client exceptions are catched and logged
      v 1.0.13 : A non serializable objet now returns null
      v 1.0.12 : add new configuratin methods / correct bugs
      v 1.0.11 : deserialize from Environment variable
      v 1.0.10 : add extension methods specifying the policy
      v 1.0.9 : add extension methods
      v 1.0.8 : add interface for http client support
      v 1.0.7 : add http client support
      v 1.0.6 : add Azure Table and Redis support
      v 1.0.5 : downgrade Logging package from 2.1 to 2.0
      v 1.0.4 : downgrade Logging package from 2.2 to 2.1
      v 1.0.3 : downgrade Logging package from 3.0 to 2.2
      v 1.0.2 : add generic deserialiser
      v 1.0.1 : add logging extensions
    </Description>
    <PackageReleaseNotes>
		Interflora .net common libraries
		v 8.29.1 : forgot to push the project file
		v 8.29.0 : Added enum BEARER so as not to modify other existing types
		v 8.28.0 : Add French Feasibility call in RAOSupplierHttpService
		v 8.27.0 : Add default Enabled property to true on the SlackAlertService + increse the default message size to 15000 characters
		v 8.26.1 : Update Lib.Common to 5.4.1 (for real)
		v 8.26.0 : Add SlackAlertService to send alerts to Slack
		v 8.25.1 : Update Lib.Common to 5.4.1
		v 8.25.0 : Update Lib.Common to 5.4.0
		v 8.24.4 : Add Delete http method to httpClient
		v 8.24.3 : Another fix for Upsert contact
		v 8.24.2 : Possible fix for handling Upsert Contact response
		v 8.24.1 : Added a log for CreateOrUpdateContactsAsync
		v 8.24.0 : Attempt to fix the parse issue for batch CreateOrUpdateContact
		v 8.23.0 : Fix CalendarHistory nullable fields and update Stock RAO Dto
		v 8.22.0 : Changed the CriticalBackgroundService to permit several instances
		v 8.21.0 : Add Rao endpoint get update calendar
		v 8.20.0 : fix issue in ListAllContactFieldsId by removing empty string in the list
		v 8.19.2 : Update on Optin for Denmark on OptinSms and OptinNewsletter
		v 8.19.1 : little logs fix on HttpClient
		v 8.19.0 : Rework HttpClient for handling retry 429 response
		v 8.18.0 : adjust Rao DTO hierarchy
		v 8.17.0 : Added new batch methods for getting and creating/updating contacts on Emarsys
		v 8.16.0 : Add OptInSms and OptInNewsletter to OrderEmail and fixed OptInOrigin getting changed every time
		v 8.15.1 : Add RAO route update
		v 8.15.0 : Add RAO http supplier for order and florist update
		v 8.14.0 : Handle Azure username before throw not found user exception
		v 8.13.2 : GSEMIG-214 - Patch
		v 8.13.1 : GSEMIG-214 - Update ITF.Lib.Common ref to 5.3.0 - Patch version
		v 8.13.0 : GSEMIG-214 - Update ITF.Lib.Common ref to 5.3.0
		v 8.12.0 : Add new DK country in Emarsys Library
		v 8.11.0 : Updated SwaggerExtensions.AddSwagger with new DocumentFilter and improved OperationFilter with feature management
		v 8.10.0 : Add Sinch service
		v 8.9.2  : Add missing setting for cancelled order event
		v 8.9.1  : add some missing settings for emarsys event_id
		v 8.9.0  : Added Iflora Messages and DTO for Emarsys Emails
		v 8.8.0  : Added Order cancelled Email event
		v 8.7.0  : Updated ITF.Lib.Common reference to 5.2.1
		v 8.6.2  : Remove logs in keycloak Service for client informations
		v 8.6.1  : Add logs in keycloak Service for client informations
		v 8.6.0  : Add logs in keycloak Service for client informations
		v 8.5.0  : Added Order rescheduled Email event
		v 8.4.0  : Add logs in keycloak Service for client informations and get User result
		v 8.3.0  : fix keycloak requests
		v 8.2.0  : improve logging for polly and reduce retry for default param
		v 8.1.0  : Update ITF.Lib.Common ref to 5.1.0
		v 8.0.2  : Rollback Googleapi lib to V4 - IMPORTANT-
		v 8.0.1  : Rollback MongoDb Version to V2
		v 8.0.0  : Update to .NET 8 / Update all libs
		v 7.46.6 : Adapt PostAsync Uri in order to have dynamic endPoint not provided in config
		v 7.46.5 : Fix AccountDeleted Event in Emarsys Library
		v 7.46.4 : Fix Delivery Reasurance in Emarsys Library
		v 7.46.3 : Add Delivery Reasurance, Order preparation in Emarsys Library
		v 7.46.2 : Add Account Deletion Notification in Emarsys Library
		v 7.46.1 : Add new Email message in Emarsys Library
		v 7.46.0 : Add Absent Recipient Email model in Emarsys Library
		v 7.45.7 : Fix checks on null values on Emarsys Library
		v 7.45.6 : Fix OptIn check on Emarsys Library
		v 7.45.5 : Fix France Country on Emarsys Library
		v 7.45.4 : Changed output of ValidateRegion
		v 7.45.3 : Fix Customer type in Emarsys Library
		v 7.45.2 : Remove unecessary log on get token autentication
		v 7.45.1 : Add more log on get token autentication
		v 7.45.0 : Updated method declaration HandleMessage in ITF.SharedLibraries.AzureServiceBus.Subscriber.IMessageHandler
		v 7.44.2 : Fix Origin consent in Emarsys Library
		v 7.44.1 : Change Consent management for Emarsys contacts
		v 7.44.0 : little refacto kafka BackgroundService + Adapt AzureServiceBus to use CriticalBackgroundService same as kafka
		v 7.43.0 : remove the creation of the error topic in kafka
		v 7.42.0 : Added QUERY_PARAM Auth method for swedish endpoints
		v 7.41.1 : Fix payload on Emarsys Library
		v 7.41.0 : Add a first implementation of an alerting system with kafka
		v 7.40.1 : Add fields on payload entities in Emarsys Library
		v 7.40.0 : Upade deps
		v 7.39.0 : Use options serializer camel case for freshPortal http request
		v 7.38.0 : Update CT SDK to latest Version
		v 7.37.0 : Add Unhealthy state + throw on KafkaException for CommitOffsetOperation from KafkaSubscriber process to kill the app
		v 7.36.0 : Add Unhealthy state + throw on ConsumeException from KafkaSubscriber process to kill the app
		v 7.35.0 : Add basic authentication configuration for Keycloak
		v 7.34.1 : Fixed Json conversion in Emarsys Library
		v 7.34.0 : Fixed Kafka topic creation when using MaxMessageBytes settings
		v 7.33.2 : Fixed Object initialization in Emarsys Library
		v 7.33.1 : Add special Optin management for Italy in Emarsys Library
		v 7.33.0 : Fixed dependencies with vuln - update packages in nuget
		v 7.32.4 : Fixed Multiple topics on one reactor
		v 7.32.3 : Fixed Null Exception on logger for HttpEmarsysService
		v 7.32.2 : Change logs on Handler integrity checks
		v 7.32.1 : Add Serialization Options parameter in Post Async method in HTTPClient Library and fixes on Emarsys Library
		v 7.32.0 : Added XWSSE Auth method for Emarsys and Emarsys Library for contact management and sending emails
		v 7.31.0 : Fix service.name to allow view the service name into apm transac into kibana
		v 7.30.0 : Added ITF.SharedLibraries.Cryptography.AesEncryption
		v 7.29.0 : Add GetAsyncString into HttpClient usefull method helper
		v 7.28.0 : Change AzureServiceBus Consumer impl
		v 7.27.0 : Change GetString To GetStringOrThrow + add a GetString that return null instead of exception into EnvironmentVariable.cs
		v 7.26.0 : Change HttpClient Extension from AddHttpClientWithPolicy with AddHttpClientWithDefaultPolicy To remove confusion with AddHttpClientWithPolicy from Polly HttpClient Extension
		v 7.25.0 : Update BackgroundService Impl for Kafka
		v 7.24.0 : Update HostBuilder Extension Method + Logging Extension
		v 7.23.2 : Fix Add a Check for GoogleMapsSettings not null and set the app UnHealthy into the AddGoogleMapsGeocodingService extension method
		v 7.23.1 : Fix Add a Check for GoogleMapsSettings not null and set the app UnHealthy into the UseGoogleMapsGeocodingService extension method
		v 7.23.0 : Add a Check for GoogleMapsSettings not null and set the app UnHealthy into the UseGoogleMapsGeocodingService extension method
		v 7.22.0 : Add a basic extention for Keyclok Authentication
		v 7.21.0 : Add Kafka settings for Max Messages Bytes in the producer config + at the topic creation
		v 7.20.2 : Some other fix about keycloak service
		v 7.20.1 : Some fix about keycloak service
		v 7.20.0 : Add keycloak service
		v 7.19.0 : upgrade lib.common version
		v 7.18.1 : Add new getAsync method with header param
		v 7.18.0 : Add getAsync contentType header param
		v 7.17.1 : Update System.Data.SqlClient from 4.8.5 to 4.8.6 (vulnerability fix)
		v 7.17.0 : Update reference MongoDB.Driver to 2.23.1
		v 7.16.0 : Fix Kafka consume message error when introduced new message
		v 7.15.0 : Deleted ITF.SharedLibraries.CommerceTools.CtOrderCustomAttributesNames (moved to IT.SharedLibraries.CT.CustomAttributes.CtOrderCustomAttributesNames)
		v 7.14.0 : Update reference
		v 7.13.0 : Added new values "comments" to /CommerceTools/CtOrderCustomAttributesNames
		v 7.12.0 : Added new values to /CommerceTools/CtOrderCustomAttributesNames
		v 7.11.0 : Added /CommerceTools/CtOrderCustomAttributesNames
		v 7.10.0 : Fix removediatrics to be in RAO legacy mode
		v 7.9.1 : Fix Kafka new params
		v 7.9.0 : Add Kafka new params
		v 7.8.0 : Update dependencies
		v 7.7.1 : Remove StopApplication Call in the ASB BackgroundWorker
		v 7.7.0 : Added configuration for http cors
		v 7.6.0 : Update dep : lib.common to use the Error class and the FunctionalExtension package
		v 7.5.0 : Add an SetUnhealthy method when we have exception in ASB to kill the app via AKS
		v 7.4.0 : Add the contentType param for httpClient in post patch and put action
		v 7.3.2 : Fix multi http client conf error in jwt token send part 2
		v 7.3.1 : Fix multi http client conf error in jwt token send
		v 7.3.0 : Add PatchAsync and PutAsync methods into generic HttpClient service with object into Body support
		v 7.2.0 : Add custom Marten projections for event sourced systems
		v 7.1.1 : Remove MediatR.Extensions.Microsoft.DependencyInjection dependency
		v 7.1.0 : Update dependencies
		v 7.0.0 : Target .net 7
		v 6.52.3 : HttpClient : retry after http 429 status error on Count operations as well
		v 6.52.2 : Kafka : remove unhealthyness on commit exception
		v 6.52.1 : HttpClient : retry after http 429 status error
		v 6.52.0 : HttpClient new throughput settings + wait on Retry-After header / Kafka unhealthyness on exception
		v 6.51.0 : Added new HttpClient extension AddHttpClientWithPolicy without retry policy
		v 6.50.0 : fix geocoding precision with adress
		v 6.49.0 : Add new health control behavior
		v 6.48.0 : Remove default logger, set Serilog as default with APM transaction span
		v 6.47.0 : Add APM and Serilog at Program.cs level with workaround
		v 6.46.1 : Rollback APM and Serilog at Program.cs level =&gt; implicit transaction are null
      v 6.46.0 : Add APM and Serilog at Program.cs level
      v 6.45.1 : Fix date reach algo / update dependencies
      v 6.45.0 : Add MongoDB default datetime serializer to localtime
      v 6.44.0 : Update dependencies
      v 6.43.0 : Update dependencies
      v 6.42.4-dev : Serilog alpha version for setting
      v 6.42.3 : Serilog downgrade to last versions
      v 6.42.2 : Serilog upgrade to last versions
      v 6.42.1 : Serilog rollback versions
      v 6.42.0 : Make Unleash not triggered if we stick with in memory feature manager / Geocode with possible missing street / Update dependencies
      v 6.41.0 : Add Marten support for ES / Update Unleash configuration
      v 6.40.1 : Kafka : fix potential deserializer error because of a non matching version in the assembly / enhance log errors
      v 6.40.0 : Add optional conf for kafka / add retentionBytes conf
      v 6.39.1 : Remove logger from date time wrapper methods, inject it instead
      v 6.39.0 : Downgrade CT lib (Breaking changes in payment interface) / remove Adobe Campaign
      v 6.38.0 : Adobe Campaign : new implementation / update Httpclient with Soap / Update dependencies
      v 6.37.0 : Timeconverter : add utc helper
      v 6.36.0 : CommerceTools : update dependencies
      v 6.35.0 : Polly : add extension methods for named policies / update dependencies
      v 6.34.1 : GoogleMaps : fix regression
      v 6.34.0 : MongoDb : add optional settings / update dependencies
      v 6.33.0 : Swagger : display authorizations (roles, policies ...) in action description if any
      v 6.32.0 : Swagger : display direct ITF lib dependencies / rollback APM 1.16 to 1.15 (Redis bug)
      v 6.31.1 : Swagger : remove tag limitations
      v 6.31.0 : APM : add Mongodb instrumentations / refactors / update dependencies
      v 6.30.0 : APM : refactor error transactions
      v 6.29.0 : HttpClient : add Put method
      v 6.28.1 : Logger fallback : fix potential null exception log
      v 6.28.0 : Provide Text.Json PascalCase configuration as part of MVC pipeline
      v 6.27.1 : Logger : remove the whole message serializer in fallback that may cause exception on ptr serializing
      v 6.27.0 : HttpClient : replace Newtonsoft with Text.Json as new standard serializer/deserializer / update dependencies
      v 6.26.0 : Redis : add Elastic APM package and tracing logs
      v 6.25.0 : MongoDB : change skip limit to use facets for best perf
      v 6.24.0 : EventstoreDb : provide projection tools
      v 6.23.0 : Update commercetools dependencies
      v 6.22.0 : EventstoreDb : rework the new grpc client + helpers / Serializer : use Text.Json as the standard for Kafka, Redis and objects / httpclient : throw on bad authentication / update dependencies
      v 6.21.0 : Kafka producer timeout conf opti
      v 6.20.0 : Kafka producer optimization / ASB opti
      v 6.19.0 : Kafka consumer : allow to configure timeout
      v 6.18.0 : Kafka consumer : allow to use default parameters
      v 6.17.0 : Add Keycloack authentication
      v 6.16.0 : Swagger : improve render performance / Httpclient : prevent null logger to throw
      v 6.15.0 : Swagger : add bearer authentication
      v 6.14.0 : Unleash : set feature flags settings non mandatory
      v 6.13.0 : Swagger : allow to provide prefix route (useful for routing operations across country)
      v 6.12.0 : Kafka : publisher and subscriber use now headers for ClrType / upadte dependencies
      v 6.11.0 : Kafka subscriber refactor + add integration tests / refactor unleash / fix logger serializer / update dependencies
      v 6.10.0 : Update dependencies
      v 6.9.0 : HttpClient : throw if http status code is unsuccess instead of trying to read/cast the stream
      v 6.8.0 : HttpClient : use extension method for serialize/deserialize for postAsync
      v 6.7.0 : HttpClient/redis : use extension method for serialize/deserialize
      v 6.6.0 : HttpClient : fix token reusable
      v 6.5.1 : Redis : fix wrong usage of revocation
      v 6.5.0 : Redis : add cache client
      v 6.4.0 : Mongo : add for sake of sample the way to hold a tcp connection alive / Add warmup extensions / update dependencies
      v 6.3.0 : Swagger : remove validator check / update dependencies
      v 6.2.0 : Little change on azureservicebus
      v 6.1.0 : Continue AzureService Bus impl
      v 6.0.1 : Fix Warmup for generic Mongo repository
      v 6.0.0 : [BREAKING CHANGES] MongoDB : remove scoped injected transaction / Warmup : allow to pass a delagate function / Swagger : fix the case where env var is empty
      v 5.7.0 : Add Swagger versioning / update dependencies
      v 5.6.0 : Add commercetools serializer and implement it in Kafka subscriber / Add Azure Service Bus implementation
      v 5.5.0 : Polly : Add exception predicates / Fix empty json config file
      v 5.4.1 : FeatureFlags : fix useless injected configuration
      v 5.4.0 : FeatureFlags : change implementation
      v 5.3.0 : FeatureFlags : add façade class + Unleash implementation / update dependencies / minor fixes
      v 5.2.0 : Kafka : throw on missing topic exception is now settable
      v 5.1.0 : MongoDB : refactor some Find queries in async mode / update dependencies
      v 5.0.1 : Fix Kafka consumer trouble (strange behavior different between workstations)
      v 5.0.0 : Target .net 6
      v 4.14.1 : Fix missing correlationId and causation Id when publishing on a Kafka method
      v 4.14.0 : Kafka : remove publish string prototype / Auto set correlationId and causation Id when publishing / update dependencies
      v 4.13.0 : APM : add helpers / add deep equals helpers / update dep
      v 4.12.0 : Mongo : add insert in replace methos in repo generic
      v 4.11.0 : Kafka : throw on unmet requirement settings / add kafka producer topic in settings
      v 4.10.0 : Update mongo lib to handle settings of database in repo
      v 4.9.0 : Update dependencies (common lib)
      v 4.8.0 : Kafka : use MessagePack for consumers
      v 4.7.0 : Kafka : add retentionMs param / add list comparison extension method
      v 4.6.1 : Kafka : add check if topic exist before create the consumer
      v 4.6.0 : Kafka : change default serializer / change some namespaces
      v 4.5.2 : fix remove config kafka max.poll.intervall.ms to default
      v 4.5.1 : fix Kakfa subscibers with handlers at null / remove log when topic already exist
      v 4.5.0 : Add Kakfa parameters and serializers / Add Polly sync policy
      v 4.4.0 : Add new split method for lists
      v 4.3.0 : Add MumurHash2 algorithm / Kafka : remove autokill from kafka consumer (driven by caller now)
      v 4.2.0 : Elasticsearch : enforce generic rules for UntypedRepository
      v 4.1.0 : Elasticsearch : create base class for POCO / ES : add extension methods for DDD/CQRS aggregate handling / MongoDB : aggregate repository
      v 4.0.0 : BREAKING CHANGES : reshape Mongo repo / change Elk mappings handling / rename DDD to Event Sourcing
      v 3.4.3 : Add HttpClient method To GetAsync specific Object direclty
      v 3.4.2 : Add JWT auth case for HttpClient Helper (for GFS API)
      v 3.4.1 : Add more information in retry callback / Add APM tag extension methods / Wrap exception checks and throwing in Elk repositories
      v 3.4.0 : Add Elk mapping extension method / Allow Kafka replaying events from date / Update dependencies
      v 3.3.0 : Add Elk mapping extension method / Add case in Serialize extension method / Update dependencies
      v 3.2.0 : Add APM tag extension methods / Provide Kafka default handling error methods / Update dependencies
      v 3.1.0 : Add APM tag extension methods / Enhance Kafka subscriber behavior / change json directory observed
      v 3.0.1 : Fix already interpolated template name for Elk index / Update dependencies
      v 3.0.0 : [BREAKING CHANGES] use external lib for base class
      v 2.7.0 : Kafka update CheckIntegrity method / Azure Blob Storage refactor / Update dependencies
      v 2.6.0 : Update dependencies
      v 2.5.0 : Add singletons WarmUp extension method
      v 2.4.0 : Add Azure blob storage repository / update dependencies
      v 2.3.0 : Elk Logging new index / Add Azure Blob Storage repository / Httpclient new methods / update dependencies
      v 2.2.0 : Add GoogleMaps geocoding / TimeConverter
      v 2.1.0 : Add APM tag transaction helper / update dependencies
      v 2.0.0 : Breaking changes : Elasticsearch no more mappings in ctor, throw wrapped exceptions / Remove useless policy in httpclient / update dependencies
      v 1.3.0 : Http : add new extension methods / Update dependencies
      v 1.2.0 : Http : release HttpResponse / Elk : change singleton register method
      v 1.1.0 : Use new semantic versioning
      v 1.0.104 : Update dependencies (shared models)
      v 1.0.103 : Kafka batch publish need provided key / Update dependencies (shared models)
      v 1.0.102 : Allow to create 'on the fly' GroupId for Kafka consumers / update dependencies
      v 1.0.101 : Add new Redis method extension
      v 1.0.100 : Allow to remove ACR from a extension method / update dependencies
      v 1.0.99 : Add UseNewtonsoft for controllers
      v 1.0.98 : Add Json Helper / Add methods Update in Elastic repo / Create a PartialUpdate repo
      v 1.0.97 : Add application lifetime ending on Kafka stop listening / add new method in Elasticsearch repo / update dependencies
      v 1.0.96 : Add http client settings and extension method / Fix time method error / Update dependencies
      v 1.0.95 : Update dependencies
      v 1.0.94 : Update logger failure callback / minor changes
      v 1.0.93 : Add more options in logger
      v 1.0.92 : Add FailureSink in logger
      v 1.0.91 : Add logger fallback logs
      v 1.0.90 : Update ITF.SharedModels dependency
      v 1.0.89 : Update dependencies / Add kafka auto create topics / Add slice in Elk repo method
      v 1.0.88 : Minor changes on ElasticSearch repository
      v 1.0.87 : Refactor logs / Update dependencies / new Kafka consumer thread like method / Break Kafka consuming from route for updates
      v 1.0.86 : Httpclient : allow to specify the odata slice
      v 1.0.85 : Add Elasticsearch methods / update readyness registration / update dependencies
      v 1.0.84 : Add extension methods for Healthchecks and Swagger/ReDoc
      v 1.0.83 : Upgrade to Upgrade to Net 5.0 / upgrade dependencies / Create Polly factory
      v 1.0.82 : Refactor log in HttpClient
      v 1.0.81 : Add Elastic APM / Set logger compatible / Code refactor / update nuget packages
      v 1.0.80 : Handle Dynamics365 token expiration / Update polly extension methods
      v 1.0.79 : Update Http Auth methods
      v 1.0.78 : HttpClient : change header setting, add odata methods / Add extensions methods / Elastic : add missing await operator, remove optional index param, add new ctor / Kafka : add batch producer, add producer new config param / Update ITF.SharedModels dependency
      v 1.0.77 : Update sharedModels lib
      v 1.0.76 : Add authMethod for HttpClient Service
      v 1.0.75 : Add dependency injection nuget packet
      v 1.0.74 : Add methods for elastic repo
      v 1.0.73 : Add catch for kafka global handler
      v 1.0.72 : Add new method to Elk repo / Add new Odata with slice method / Add interface to httpClient / Serilog use appsettings for config / Add new HostBuilder (get rid of Vault)
      v 1.0.71 : Encapsulate configuration when using HttpClient
      v 1.0.70 : Add Readyness route extension method / On Elk repository throw an exception on failure
      v 1.0.69 : Update Shared Model dependency
      v 1.0.68 : Implement 'typed object' in Kafka to use Matching Pattern in handlers
      v 1.0.67 : Update Elasticsearch repository / remove useless Logger / remove middleware from metrics extension methods
      v 1.0.66 : Remove Eventgrid / Add scoped log for Jaeger / Add AppMetrics with Prometheus
      v 1.0.65 : Add healthcheck for Elk and Kafka / Add Jaeger and OpenTracing for distributed traces and logs
      v 1.0.64 : Remove shared models to dedicated lib / Add ElasticSearch support
      v 1.0.63 : New project folder architecture
      v 1.0.62 : Add await on error handling of Kafka multiple subscriber / remove autogenerated elements
      v 1.0.61 : Remove auto generated assemby info
      v 1.0.60 : Add Kafka Producer and Consumer infrastucture part
      v 1.0.59 : Add healthchecks and metrics / Simplify Hashicorp Vault builder
      v 1.0.58 : Implement postgres repository with EF Core / Add Projection for Postgres
      v 1.0.57 : Add Projection from EventStore infrastructure
      v 1.0.56 : Little fix repo postgres
      v 1.0.55 : Add GenericRepo Postgres + Dapper
      v 1.0.54 : Add Domain Driven Design infrastructure part
      v 1.0.53 : Update MongoRepo
      v 1.0.52 : Allow multiple instance register of RabbitMQ handlers
      v 1.0.51 : RabbitMQ minor change for Adding handler separately
      v 1.0.50 : Change RabbitMQ supplier registration + check integrity
      v 1.0.49 : RabbitMQ new extension method / configuration and sub association made by ClassName
      v 1.0.48 : Add error handling for RabbitMq
      v 1.0.47 : Change RabbitMQ subscribers declaration / minor changes in Hashicorp vault
      v 1.0.46 : Implementation of Hashicorp Vault configuration provider with hotreload / FeatureManagement / LogLevel
      v 1.0.45 : Update MongoRepo
      v 1.0.44 : Add method into MongoRepo
      v 1.0.43 : Change ToList into MongoDbRepo
      v 1.0.42 : Add OrderMessageType Enum and change nullable ExpiryTime on BaseMessage
      v 1.0.41 : Add some enums and models for message RabbitMq
      v 1.0.40 : RabbitMQ new subscribing implementation
      v 1.0.39 : Add first implementation of MongoDb
      v 1.0.38 : Little change RabbitMQ setup
      v 1.0.37 : Add RabbitMQ consumers
      v 1.0.36 : Add RabbitMQ client
      v 1.0.35 : Hashicorp vault client serialize dictionnary / Add logger factory for ELK and Seq
      v 1.0.34 : HttpClient class has no more generic ctor, only genric methods / add support for Hashicorp Vault
      v 1.0.33 : Simplify HttpClient class
      v 1.0.32 : Redis client should now be used as a singleton / log by level
      v 1.0.31 : Add BatchGuid for Notif
      v 1.0.30 : Add batches for enumerables
      v 1.0.29 : Throw exceptions on bad token for Adobe token client
      v 1.0.28 : Increase log level for Adobe token client
      v 1.0.27 : Change properties naming
      v 1.0.26 : Add Missing interface for Adobe client
      v 1.0.25 : Add MissingRecipient template for SOAP Adobe call
      v 1.0.24 : Add DeleteAll method for Azure Table
      v 1.0.23 : Suppress 404 cases in Polly Policy
      v 1.0.22 : Change HttpClient timeout / log error in Adobe Client
      v 1.0.21 : Adobe client now log exceptions
      v 1.0.20 : NotifDto is shared in lib
      v 1.0.19 : Add shared objects and enum
      v 1.0.18 : Adobe Campaign fix missing method in interface
      v 1.0.17 : Adobe Campaign configuration objects
      v 1.0.16 : Add Adobe Campaign client helper
      v 1.0.15 : Create logger from injected factory
      v 1.0.14 : Redis client exceptions are catched and logged
      v 1.0.13 : A non serializable objet now returns null
      v 1.0.12 : add new configuratin methods / correct bugs
      v 1.0.11 : deserialize from Environment variable
      v 1.0.10 : add extension methods specifying the policy
      v 1.0.9 : add extension methods
      v 1.0.8 : add interface for http client support
      v 1.0.7 : add http client support
      v 1.0.6 : add Azure Table and Redis support
      v 1.0.5 : downgrade Logging package from 2.1 to 2.0
      v 1.0.4 : downgrade Logging package from 2.2 to 2.1
      v 1.0.3 : downgrade Logging package from 3.0 to 2.2
      v 1.0.2 : add generic deserialiser
      v 1.0.1 : add logging extensions
    </PackageReleaseNotes>
    <PackageTags>Git on Azure Devops</PackageTags>
    <RepositoryUrl>https://interflorad365fo.visualstudio.com/ITF.SharedLibraries/_git/ITF.SharedLibraries</RepositoryUrl>
    <Copyright>Interflora France</Copyright>
    <Version>8.29.1</Version>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Postgres\BaseEntity\**" />
    <EmbeddedResource Remove="Postgres\BaseEntity\**" />
    <None Remove="Postgres\BaseEntity\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="App.Metrics.AspNetCore.All" Version="4.3.0" />
    <PackageReference Include="App.Metrics.AspNetCore.Endpoints" Version="4.3.0" />
    <PackageReference Include="App.Metrics.AspNetCore.Tracking" Version="4.3.0" />
    <PackageReference Include="App.Metrics.Extensions.DependencyInjection" Version="4.3.0" />
    <PackageReference Include="App.Metrics.Extensions.Hosting" Version="4.3.0" />
    <PackageReference Include="App.Metrics.Formatters.Prometheus" Version="4.3.0" />
    <PackageReference Include="App.Metrics.Prometheus" Version="4.3.0" />
    <PackageReference Include="AspNetCore.HealthChecks.Elasticsearch" Version="8.0.1" />
    <PackageReference Include="AspNetCore.HealthChecks.EventStore" Version="8.0.1" />
    <PackageReference Include="AspNetCore.HealthChecks.Kafka" Version="8.0.1" />
    <PackageReference Include="AspNetCore.HealthChecks.MongoDb" Version="8.1.0" />
    <PackageReference Include="AspNetCore.HealthChecks.NpgSql" Version="8.0.2" />
    <PackageReference Include="AspNetCore.HealthChecks.Rabbitmq" Version="8.0.2" />
    <PackageReference Include="AspNetCore.HealthChecks.UI.Client" Version="8.0.1" />
    <PackageReference Include="Azure.Messaging.ServiceBus" Version="7.18.2" />
    <PackageReference Include="Azure.Storage.Blobs" Version="12.23.0" />
    <PackageReference Include="commercetools.Sdk.Api" Version="11.14.0" />
    <PackageReference Include="Confluent.Kafka" Version="2.6.1" />
    <PackageReference Include="Dapper" Version="2.1.35" />
    <PackageReference Include="Elastic.Apm.NetCoreAll" Version="1.30.1" />
    <PackageReference Include="Elastic.Apm.SerilogEnricher" Version="8.12.2" />
    <PackageReference Include="Elastic.Apm.StackExchange.Redis" Version="1.30.1" />
    <PackageReference Include="Elastic.CommonSchema.Serilog" Version="8.12.2" />
    <PackageReference Include="EventStore.Client.Grpc.Streams" Version="23.3.7" />
    <PackageReference Include="Jaeger" Version="1.0.3" />
    <PackageReference Include="Marten" Version="6.4.1" />
    <PackageReference Include="MediatR" Version="12.4.1" />
    <PackageReference Include="MessagePack" Version="2.5.192" />
    <PackageReference Include="Microsoft.ApplicationInsights" Version="2.22.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.11" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning" Version="5.1.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning.ApiExplorer" Version="5.1.0" />
    <PackageReference Include="Microsoft.Azure.Cosmos.Table" Version="1.0.8" />
    <PackageReference Include="Microsoft.Azure.EventGrid" Version="3.2.1" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyModel" Version="8.0.2" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Http.Polly" Version="8.0.11" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.ObjectPool" Version="8.0.11" />
    <PackageReference Include="Microsoft.FeatureManagement" Version="4.0.0" />
    <PackageReference Include="Microsoft.FeatureManagement.AspNetCore" Version="4.0.0" />
    <PackageReference Include="Microsoft.Rest.ClientRuntime" Version="2.3.24" />
    <PackageReference Include="MongoDB.Driver" Version="2.30.0" />
    <PackageReference Include="NEST" Version="7.17.5" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Npgsql" Version="8.0.6" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="8.0.11" />
    <PackageReference Include="Polly" Version="8.5.0" />
    <PackageReference Include="Polly.Extensions.Http" Version="3.0.0" />
    <PackageReference Include="prometheus-net" Version="8.2.1" />
    <PackageReference Include="prometheus-net.AspNetCore" Version="8.2.1" />
    <PackageReference Include="prometheus-net.AspNetCore.HealthChecks" Version="8.2.1" />
    <PackageReference Include="RabbitMQ.Client" Version="7.0.0" />
    <PackageReference Include="Schick.Keycloak.RestApiClient" Version="26.0.2" />
    <PackageReference Include="Serilog" Version="4.1.0" />
    <PackageReference Include="Serilog.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="Serilog.Extensions.Logging" Version="8.0.0" />
    <PackageReference Include="Serilog.Settings.Configuration" Version="8.0.4" />
    <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageReference Include="Serilog.Sinks.ElasticSearch" Version="10.0.0" />
    <PackageReference Include="Serilog.Sinks.Seq" Version="8.0.0" />
    <PackageReference Include="Sinch" Version="1.1.2" />
    <PackageReference Include="StackExchange.Redis" Version="2.8.16" />
    <PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="7.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore.Newtonsoft" Version="7.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore.ReDoc" Version="7.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore.Swagger" Version="7.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore.SwaggerUI" Version="7.0.0" />
    <PackageReference Include="System.ComponentModel.Annotations" Version="5.0.0" />
    <PackageReference Include="System.Data.SqlClient" Version="4.9.0" />
    <PackageReference Include="System.Diagnostics.DiagnosticSource" Version="8.0.1" />
    <PackageReference Include="System.Net.Http" Version="4.3.4" />
    <PackageReference Include="System.Text.Json" Version="8.0.5" />
    <PackageReference Include="System.Text.RegularExpressions" Version="4.3.1" />
    <PackageReference Include="Unleash.Client" Version="5.0.1" />
    <PackageReference Include="VaultSharp" Version="1.17.5.1" />
    <PackageReference Include="GoogleApi" Version="4.5.3" />
    <PackageReference Include="TimeZoneConverter" Version="6.1.0" />
    <PackageReference Include="Utf8Json" Version="1.3.7" />
    <PackageReference Include="libphonenumber-csharp" Version="8.13.50" />
    <PackageReference Include="ITF.Lib.Common" Version="5.4.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\ITF.Lib.Common\src\ITF.Lib.Common.csproj" />
  </ItemGroup>

</Project>
