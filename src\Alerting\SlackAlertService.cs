using ITF.SharedLibraries.Kafka;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Polly;
using Polly.CircuitBreaker;
using Polly.Retry;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;

namespace ITF.SharedLibraries.Alerting;

/// <summary>
/// Represents the different environment levels for alert filtering
/// </summary>
public enum AlertEnvironment
{
    Dev,
    Recette,
    Perf,
    Preprod,
    Prod,
    Development
}

/// <summary>
/// Represents the different country codes for alert filtering
/// </summary>
public enum AlertCountry
{
    Fr,
    It,
    Es,
    Pt,
    Dk,
    Se
}

/// <summary>
/// Represents the alert level configuration containing allowed environments and countries
/// </summary>
public class AlertLevel
{
    public HashSet<AlertEnvironment> AllowedEnvironments { get; set; }
    public HashSet<AlertCountry> AllowedCountries { get; set; }

    public AlertLevel(params AlertEnvironment[] environments)
    {
        AllowedEnvironments = [.. environments];
        AllowedCountries = [];
    }

    public AlertLevel(IEnumerable<AlertEnvironment> environments)
    {
        AllowedEnvironments = [.. environments];
        AllowedCountries = [];
    }

    public AlertLevel(IEnumerable<AlertEnvironment> environments, IEnumerable<AlertCountry> countries)
    {
        AllowedEnvironments = [.. environments];
        AllowedCountries = [.. countries];
    }

    public AlertLevel(AlertEnvironment[] environments, AlertCountry[] countries)
    {
        AllowedEnvironments = [.. environments];
        AllowedCountries = [.. countries];
    }
}

/// <summary>
/// Configuration for the Slack alert service
/// </summary>
public class SlackAlertOptions
{
    public const string ConfigSection = "SlackAlert";

    /// <summary>
    /// The Slack API token
    /// </summary>
    public string ApiToken { get; set; } = string.Empty; // ********************************************************

    /// <summary>
    /// The default channel to post messages to
    /// </summary>
    public string DefaultChannel { get; set; } = "alerts-ms-fr";

    /// <summary>
    /// The Url from slack to post messages to
    /// </summary>
    public string SendMessageUrl { get; set; } = "https://slack.com/api/chat.postMessage";

    /// <summary>
    /// The name of the bot that will post messages
    /// </summary>
    public string BotName { get; set; } = "Error Alert Bot";

    /// <summary>
    /// The emoji to use as the bot's icon
    /// </summary>
    public string BotEmoji { get; set; } = ":warning:";

    /// <summary>
    /// The maximum number of retry attempts for failed notifications
    /// </summary>
    public int MaxRetryAttempts { get; set; } = 3;

    /// <summary>
    /// The initial delay between retry attempts in milliseconds
    /// </summary>
    public int RetryInitialDelayMs { get; set; } = 1000;

    /// <summary>
    /// The timeout for Slack API requests in milliseconds
    /// </summary>
    public int TimeoutMs { get; set; } = 10000;

    /// <summary>
    /// Whether to include stack traces in error messages
    /// </summary>
    public bool IncludeStackTrace { get; set; } = true;

    /// <summary>
    /// Maximum length for error message content before truncation
    /// </summary>
    public int MaxMessageLength { get; set; } = 15000;

    /// <summary>
    /// Whether to use a fallback mechanism if Slack is unavailable
    /// </summary>
    public bool EnableFallbackLogging { get; set; } = true;

    /// <summary>
    /// How many failures before tripping the circuit breaker
    /// </summary>
    public int CircuitBreakerThreshold { get; set; } = 5;

    /// <summary>
    /// Duration in seconds to keep the circuit breaker open before trying again
    /// </summary>
    public int CircuitBreakerDurationSeconds { get; set; } = 60;

    /// <summary>
    /// Global flag to enable or disable the Slack alert service
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// List of environment names that should receive alerts by default (when no AlertLevel is specified in method calls)
    /// If empty or null, alerts will be sent to all environments
    /// </summary>
    public List<string>? DefaultAllowedEnvironments { get; set; }

    /// <summary>
    /// List of country codes that should receive alerts by default (when no AlertLevel is specified in method calls)
    /// If empty or null, alerts will be sent to all countries
    /// </summary>
    public List<string>? DefaultAllowedCountries { get; set; }
}

public interface ISlackAlertService
{
    /// <summary>
    /// Sends an error alert to Slack
    /// </summary>
    /// <param name="errorMessage">The main error message</param>
    /// <param name="exception">The exception that occurred (optional)</param>
    /// <param name="channel">The Slack channel to post to (overrides default)</param>
    /// <param name="alertLevel">The alert level specifying which environments should receive this alert (optional)</param>
    /// <returns>A task representing the send operation</returns>
    Task SendErrorAlertAsync(string errorMessage, Exception? exception = null, string? channel = null, AlertLevel? alertLevel = null);

    /// <summary>
    /// Sends a custom alert message to Slack with optional attachments
    /// </summary>
    /// <param name="title">The title of the alert</param>
    /// <param name="message">The alert message</param>
    /// <param name="fields">Additional fields to include in the message</param>
    /// <param name="color">The color of the message border (default: danger/red)</param>
    /// <param name="channel">The Slack channel to post to (overrides default)</param>
    /// <param name="alertLevel">The alert level specifying which environments should receive this alert (optional)</param>
    /// <returns>A task representing the send operation</returns>
    Task SendCustomAlertAsync(string title, string message,
        Dictionary<string, string>? fields = null,
        string? color = "danger",
        string? channel = null,
        AlertLevel? alertLevel = null);

    /// <summary>
    /// Checks if the Slack service is available
    /// </summary>
    /// <returns>True if the service is available</returns>
    Task<bool> IsServiceAvailableAsync();
}
/// <summary>
/// Implementation of the Slack alert service
/// </summary>
public class SlackAlertService : ISlackAlertService, IDisposable
{
    private readonly System.Net.Http.HttpClient _httpClient;
    private readonly ILogger<SlackAlertService> _logger;
    private readonly SlackAlertOptions _options;
    private readonly AsyncRetryPolicy _retryPolicy;
    private readonly AsyncCircuitBreakerPolicy _circuitBreakerPolicy;
    private readonly SemaphoreSlim _semaphore = new(1, 1);
    private bool _disposed = false;

    /// <summary>
    /// Constructs a new SlackAlertService
    /// </summary>
    public SlackAlertService(
        IOptionsMonitor<SlackAlertOptions> options,
        ILogger<SlackAlertService> logger,
        IHttpClientFactory httpClientFactory)
    {
        _options = options?.CurrentValue ?? throw new ArgumentNullException(nameof(options), "Impossible to create the SlackAlertService missing SlackAlertOptions config");
        _logger = logger ?? throw new ArgumentNullException(nameof(logger), "Impossible to create the SlackAlertService");

        // Validate essential configuration
        if (string.IsNullOrWhiteSpace(_options.ApiToken))
            _logger.LogError("Error in Ctor SlackAlertService: Slack API token is not configured");

        if (string.IsNullOrWhiteSpace(_options.DefaultChannel))
            _logger.LogError("Error in Ctor SlackAlertService: Default Slack channel is not configured");

        if (string.IsNullOrWhiteSpace(_options.SendMessageUrl))
            _logger.LogError("Error in Ctor SlackAlertService: Default Slack Url is not configured");

        // Setup HTTP client
        _httpClient = httpClientFactory?.CreateClient("SlackAlertService") ?? throw new ArgumentNullException(nameof(httpClientFactory), "Impossible to create the SlackAlertService httpClientFactory null");
        _httpClient.Timeout = TimeSpan.FromMilliseconds(_options.TimeoutMs);
        _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", _options.ApiToken);

        // Configure retry policy with exponential backoff
        _retryPolicy = Policy
            .Handle<HttpRequestException>()
            .Or<TaskCanceledException>()
            .Or<TimeoutException>()
            .WaitAndRetryAsync(
                _options.MaxRetryAttempts,
                retryAttempt => TimeSpan.FromMilliseconds(_options.RetryInitialDelayMs * Math.Pow(2, retryAttempt - 1)),
                (exception, timeSpan, retryCount, context) =>
                {
                    _logger.LogWarning(
                        exception,
                        "Failed to send Slack alert. Retry attempt {RetryCount} after {RetryTimeSpan}ms",
                        retryCount,
                        timeSpan.TotalMilliseconds);
                });

        // Configure circuit breaker policy
        _circuitBreakerPolicy = Policy
            .Handle<HttpRequestException>()
            .Or<TaskCanceledException>()
            .Or<TimeoutException>()
            .CircuitBreakerAsync(
                exceptionsAllowedBeforeBreaking: _options.CircuitBreakerThreshold,
                durationOfBreak: TimeSpan.FromSeconds(_options.CircuitBreakerDurationSeconds),
                onBreak: (ex, breakDuration) =>
                {
                    _logger.LogError(
                        ex,
                        "Circuit breaker tripped for Slack alert service. Breaking for {BreakDuration} seconds",
                        breakDuration.TotalSeconds);
                },
                onReset: () =>
                {
                    _logger.LogInformation("Circuit breaker reset for Slack alert service");
                },
                onHalfOpen: () =>
                {
                    _logger.LogInformation("Circuit breaker half-open for Slack alert service");
                });

        _logger.LogInformation("SlackAlertService initialized with default channel {Channel}", _options.DefaultChannel);
    }

    /// <summary>
    /// Generates the dynamic channel name based on country and environment
    /// Pattern: alerts-ms-{country}-{env} (special cases: prod removes -env, Development becomes debug)
    /// </summary>
    /// <returns>The generated channel name or default channel if generation fails</returns>
    private string GetDynamicChannelName()
    {
        try
        {
            var countryString = Environment.GetEnvironmentVariable("ASPNETCORE_COUNTRY")?.ToLowerInvariant();
            var environmentString = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")?.ToLowerInvariant();

            if (string.IsNullOrWhiteSpace(countryString) || string.IsNullOrWhiteSpace(environmentString))
            {
                _logger.LogDebug("Country or Environment not set, using default channel");
                return _options.DefaultChannel;
            }

            // Try to parse country
            if (!Enum.TryParse<AlertCountry>(countryString, true, out var country))
            {
                _logger.LogDebug("Unknown country '{Country}', using default channel", countryString);
                return _options.DefaultChannel;
            }

            // Try to parse environment
            if (!Enum.TryParse<AlertEnvironment>(environmentString, true, out var environment))
            {
                _logger.LogDebug("Unknown environment '{Environment}', using default channel", environmentString);
                return _options.DefaultChannel;
            }

            // Generate channel name based on rules
            var countryCode = country.ToString().ToLowerInvariant();

            return environment switch
            {
                AlertEnvironment.Prod => $"alerts-ms-{countryCode}",
                AlertEnvironment.Development => "alerts-ms-debug",
                _ => $"alerts-ms-{countryCode}-{environment.ToString().ToLowerInvariant()}"
            };
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to generate dynamic channel name, using default channel");
            return _options.DefaultChannel;
        }
    }

    /// <summary>
    /// Checks if the current environment and country should receive alerts based on the provided AlertLevel or default configuration
    /// </summary>
    /// <param name="alertLevel">The alert level specifying allowed environments and countries (optional)</param>
    /// <returns>True if the alert should be sent, false otherwise</returns>
    private bool ShouldSendAlert(AlertLevel? alertLevel)
    {
        var currentEnvironmentString = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")?.ToLowerInvariant();
        var currentCountryString = Environment.GetEnvironmentVariable("ASPNETCORE_COUNTRY")?.ToLowerInvariant();

        if (string.IsNullOrWhiteSpace(currentEnvironmentString))
        {
            _logger.LogWarning("ASPNETCORE_ENVIRONMENT is not set, defaulting to sending alert in all environments");
            return true;
        }

        // Try to parse the current environment
        if (!Enum.TryParse<AlertEnvironment>(currentEnvironmentString, true, out var currentEnvironment))
        {
            _logger.LogWarning("Unknown environment '{Environment}', defaulting to sending alert in all environments", currentEnvironmentString);
            return true;
        }

        // Try to parse the current country (optional)
        AlertCountry? currentCountry = null;
        if (!string.IsNullOrWhiteSpace(currentCountryString))
        {
            if (Enum.TryParse<AlertCountry>(currentCountryString, true, out var parsedCountry))
            {
                currentCountry = parsedCountry;
            }
            else
            {
                _logger.LogWarning("Unknown country '{Country}', ignoring country filtering", currentCountryString);
            }
        }

        // If AlertLevel is provided in method call, use it (highest priority)
        if (alertLevel != null)
        {
            var environmentAllowed = alertLevel.AllowedEnvironments.Contains(currentEnvironment);

            // If no countries specified in AlertLevel, only check environment
            if (alertLevel.AllowedCountries.Count == 0)
            {
                return environmentAllowed;
            }

            // If countries are specified, check both environment and country
            var countryAllowedInAlert = currentCountry.HasValue && alertLevel.AllowedCountries.Contains(currentCountry.Value);
            return environmentAllowed && countryAllowedInAlert;
        }

        // Check default configuration
        var envAllowed = true;
        var countryAllowed = true;

        // Check default allowed environments
        if (_options.DefaultAllowedEnvironments != null && _options.DefaultAllowedEnvironments.Count > 0)
        {
            var defaultAllowedEnvs = _options.DefaultAllowedEnvironments
                .Where(env => Enum.TryParse<AlertEnvironment>(env, true, out _))
                .Select(env => Enum.Parse<AlertEnvironment>(env, true))
                .ToHashSet();

            envAllowed = defaultAllowedEnvs.Contains(currentEnvironment);
        }

        // Check default allowed countries
        if (_options.DefaultAllowedCountries != null && _options.DefaultAllowedCountries.Count > 0 && currentCountry.HasValue)
        {
            var defaultAllowedCountries = _options.DefaultAllowedCountries
                .Where(country => Enum.TryParse<AlertCountry>(country, true, out _))
                .Select(country => Enum.Parse<AlertCountry>(country, true))
                .ToHashSet();

            countryAllowed = defaultAllowedCountries.Contains(currentCountry.Value);
        }

        return envAllowed && countryAllowed;
    }

    /// <inheritdoc/>
    public async Task SendErrorAlertAsync(string errorMessage, Exception? exception = null, string? channel = null, AlertLevel? alertLevel = null)
    {
        if (!_options.Enabled)
        {
            return;
        }

        // Check if the current environment and country should receive this alert
        if (!ShouldSendAlert(alertLevel))
        {
            _logger.LogDebug("Alert not sent due to environment/country filtering. Current environment or country not in allowed list.");
            return;
        }

        if (string.IsNullOrEmpty(errorMessage))
        {
            _logger.LogError("Error in SendErrorAlertAsync: no errorMessage provided");
            return;
        }

        if (string.IsNullOrWhiteSpace(_options.ApiToken))
        {
            _logger.LogError("Error in SendErrorAlertAsync: Slack API token is not configured");
            return;
        }

        var fields = new Dictionary<string, string>
        {
            // Add basic information
            { "Timestamp", DateTimeOffset.UtcNow.ToString("yyyy-MM-dd HH:mm:ss 'UTC'") },
            { "Application", AppDomain.CurrentDomain.FriendlyName },
            { "Environment", Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Unknown" },
            { "Country", Environment.GetEnvironmentVariable("ASPNETCORE_COUNTRY") ?? "Unknown" }
        };

        // Add exception details if available
        if (exception != null)
        {
            fields.Add("Exception Type", exception.GetType().Name);

            if (!string.IsNullOrEmpty(exception.Source))
            {
                fields.Add("Source", exception.Source);
            }

            if (_options.IncludeStackTrace && !string.IsNullOrEmpty(exception.StackTrace))
            {
                // Truncate stack trace if needed
                var stackTrace = exception.StackTrace;
                if (stackTrace.Length > _options.MaxMessageLength / 2)
                    stackTrace = string.Concat(stackTrace.AsSpan(0, _options.MaxMessageLength / 2), "... (truncated)");
                fields.Add("Stack Trace", stackTrace);
            }

            // Add inner exception if available
            if (exception.InnerException != null)
                fields.Add("Inner Exception", exception.InnerException.Message);
        }

        await SendCustomAlertAsync(
            "Error Alert",
            errorMessage,
            fields,
            "danger",
            channel,
            alertLevel);
    }

    /// <inheritdoc/>
    public async Task SendCustomAlertAsync(
        string title,
        string message,
        Dictionary<string, string>? fields = null,
        string? color = "danger",
        string? channel = null,
        AlertLevel? alertLevel = null)
    {
        if (!_options.Enabled)
        {
            return;
        }

        // Check if the current environment and country should receive this alert
        if (!ShouldSendAlert(alertLevel))
        {
            _logger.LogDebug("Alert not sent due to environment/country filtering. Current environment or country not in allowed list.");
            return;
        }

        if (string.IsNullOrWhiteSpace(title))
        {
            _logger.LogError("Error in SendCustomAlertAsync: no title provided");
            return;
        }

        if (string.IsNullOrWhiteSpace(message))
        {
            _logger.LogError("Error in SendCustomAlertAsync: no message provided");
            return;
        }

        if (string.IsNullOrWhiteSpace(_options.ApiToken))
        {
            _logger.LogError("Error in SendCustomAlertAsync: Slack API token is not configured");
            return;
        }

        // Use dynamic channel generation if none specified
        channel ??= GetDynamicChannelName();

        try
        {
            await _semaphore.WaitAsync();

            // Truncate message if it's too long
            if (message.Length > _options.MaxMessageLength)
                message = string.Concat(message.AsSpan(0, _options.MaxMessageLength), "... (message truncated)");

            // Construct Slack message payload
            var payload = new
            {
                channel,
                username = _options.BotName,
                icon_emoji = _options.BotEmoji,
                attachments = new[]
                {
                        new
                        {
                            fallback = $"{title}: {message}",
                            color,
                            title,
                            text = message,
                            fields = fields?.Select(f => new
                            {
                                title = f.Key,
                                value = f.Value,
                                @short = f.Value.Length < 50
                            }).ToArray(),
                            ts = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
                        }
                    }
            };

            var jsonPayload = JsonSerializer.Serialize(payload);
            var content = new StringContent(jsonPayload, Encoding.UTF8, "application/json");

            // Use policies to make the request resilient
            await Policy.WrapAsync(_circuitBreakerPolicy, _retryPolicy)
                .ExecuteAsync(async () =>
                {
                    var response = await _httpClient.PostAsync(_options.SendMessageUrl, content);

                    if (!response.IsSuccessStatusCode)
                    {
                        var responseContent = await response.Content.ReadAsStringAsync();
                        throw new HttpRequestException(
                            $"Failed to send Slack alert. Status code: {response.StatusCode}, Response: {responseContent}");
                    }

                    var responseJson = await response.Content.ReadAsStringAsync();
                    var responseObj = JsonSerializer.Deserialize<Dictionary<string, JsonElement>>(responseJson);

                    if ((responseObj?.TryGetValue("ok", out var okValue) ?? false) && !okValue.GetBoolean())
                    {
                        string? errorMessage = "Unknown error";
                        if (responseObj.TryGetValue("error", out var errorValue))
                            errorMessage = errorValue.GetString();

                        throw new InvalidOperationException($"Slack API error: {errorMessage}");
                    }
                });

            _logger.LogDebug("Successfully sent Slack alert to channel {Channel}", channel);
        }
        catch (BrokenCircuitException ex)
        {
            _logger.LogError(ex, "Circuit is open, not attempting to send Slack alert");

            if (_options.EnableFallbackLogging)
                LogFallbackMessage(title, message, fields);

            // Don't rethrow - we want to degrade gracefully
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send Slack alert");

            if (_options.EnableFallbackLogging)
                LogFallbackMessage(title, message, fields);

            // Don't rethrow - we want to degrade gracefully
        }
        finally
        {
            _semaphore.Release();
        }
    }

    /// <inheritdoc/>
    public async Task<bool> IsServiceAvailableAsync()
    {
        try
        {
            // Simple API test call to check if Slack is available
            var request = new HttpRequestMessage(HttpMethod.Get, "https://slack.com/api/auth.test");
            var response = await _httpClient.SendAsync(request);

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var result = JsonSerializer.Deserialize<Dictionary<string, JsonElement>>(content);

                if (result?.TryGetValue("ok", out var okValue) ?? false)
                {
                    return okValue.GetBoolean();
                }
            }

            return false;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Logs a message to the fallback logger when Slack is unavailable
    /// </summary>
    private void LogFallbackMessage(string title, string message, Dictionary<string, string>? fields)
    {
        var sb = new StringBuilder();
        sb.AppendLine($"FALLBACK SLACK ALERT - {title}");
        sb.AppendLine(message);

        if (fields != null && fields.Count > 0)
        {
            sb.AppendLine("Additional information:");
            foreach (var field in fields)
            {
                sb.AppendLine($"- {field.Key}: {field.Value}");
            }
        }

        _logger.LogError("{SlackMessage}", sb.ToString());
    }

    /// <summary>
    /// Disposes the service and releases resources
    /// </summary>
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// Releases the unmanaged resources used by the service and optionally releases the managed resources
    /// </summary>
    protected virtual void Dispose(bool disposing)
    {
        if (_disposed)
            return;

        if (disposing)
        {
            _semaphore?.Dispose();
        }

        _disposed = true;
    }
}

/// <summary>
/// Extension methods for service registration
/// </summary>
public static class SlackAlertServiceExtensions
{
    /// <summary>
    /// Adds the Slack alert service to the service collection
    /// </summary>
    public static IServiceCollection AddSlackAlertService(this IServiceCollection services, IConfiguration configuration)
    {
        // Ensure configuration is valid
        //var options = configuration.GetSection(SlackAlertOptions.ConfigSection).Get<SlackAlertOptions>() ?? throw new InvalidOperationException(
        //        $"Missing configuration section '{SlackAlertOptions.ConfigSection}' for SlackAlertService");

        //if (string.IsNullOrWhiteSpace(options.ApiToken))
        //    throw new InvalidOperationException("Slack API token must be configured");

        //if (string.IsNullOrWhiteSpace(options.DefaultChannel))
        //    throw new InvalidOperationException("Default Slack channel must be configured");

        // Register options
        services.Configure<SlackAlertOptions>(
            configuration.GetSection(SlackAlertOptions.ConfigSection));

        // Register HTTP client
        services.AddHttpClient("SlackAlertService", client =>
        {
            client.DefaultRequestHeaders.Accept.Add(
                new MediaTypeWithQualityHeaderValue("application/json"));
        });

        // Register service as singleton (maintains circuit breaker state)
        services.AddSingleton<ISlackAlertService, SlackAlertService>();

        return services;
    }
}
