﻿namespace IT.Microservices.CT.Order.Wrapper.Infrastructure.Settings;
public class CTOrderWrapperSettings
{
    public string CountryCode { get; set; }
    public string Language { get; set; }
    public string PFsGetOrderDocumentUrlFormat { get; set; }
    public bool SequenceNumberNonNumeric { get; set; }
    
    public FrenchSettings FrenchSettings { get; set; } = new FrenchSettings();
}

public class FrenchSettings
{
    public string OrderNumberPrefix { get; set; }
    public int ResultsReturnedByAvailability { get; set; }
    public int MorningLimitHour { get; set; }
    public int MorningLimitMinutes { get; set; }
    public int AfternoonLimitHour { get; set; }
    public int AfternoonLimitMinutes { get; set; }
    public decimal ProductMinPrice { get; set; }
    public decimal ProductMaxPrice { get; set; }
}
