{
  "AllowedHosts": "*",
  "Serilog": {
    "Using": [],
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Warning",
        "System": "Information",
        "Elastic": "Warning",
        "Apm": "Warning"
      }
    },
    "WriteTo": [
      {
        "Name": "Console"
      }
    ],
    "Enrich": [
      "FromLogContext",
      "WithMachineName",
      "WithProcessId",
      "WithThreadId"
    ],
    "Properties": {
      "ApplicationName": "IT.Microservices.CT.Order.Wrapper"
    }
  },
  "ElasticApm": {
    "ServerUrl": "**********",
    "Enabled": true,
    "TransactionSampleRate": 1,
    "CaptureBody": "all",
    "CaptureHeaders": true,
    "SpanFramesMinDuration": 0, // no stacktrace except for exception
    "CloudProvider": "none"
  },
  "ElasticSearchLog": {
    "ElasticSearchLog": "**********"
  },
  "Unleash": {
    "Url": "**********",
    "ProjectId": "default",
    "ApplicationName": "IT.Microservices.CT.Order.Wrapper",
    "FetchTogglesIntervalInSeconds": 15,
    "SendMetricsIntervalInSeconds": 30,
    "Environment": "development"
  },
  "FeatureFlags": {
    "Provider": "featuremanager"
  },
  "LegacyBackendSlaveEndpoint": {
    "Authentication": {
      "Credentials": {
        "username": "",
        "password": ""
      },
      "URL": "",
      "AuthMethod": "",
      "UseExpirationTime": true
    },
    "Url": "",
    "HttpTimeoutInSeconds": 700,
    "PolicyTimeoutInSeconds": 250,
    "HandlerLifetime": 5,
    "DefaultConnectionLimit": 10
  },
  "LegacyBackendSlaveEndpointList": {
    "OrderCheckUpdate": {
      "Action": "",
      "Endpoint": ""
    }
  },

  "LegacyBackendEndpoint": {
    "Authentication": {
      "Credentials": {
        "grant_type": "",
        "resource": ""
      },
      "URL": "",
      "AuthMethod": "",
      "UseExpirationTime": true
    },
    "Url": "",
    "HttpTimeoutInSeconds": 700,
    "PolicyTimeoutInSeconds": 250,
    "HandlerLifetime": 5,
    "DefaultConnectionLimit": 10
  },
  "LegacyBackendEndpointList": {
    "OrderCheckUpdate": {
      "Action": "",
      "Endpoint": ""
    }
  },
  "RAOEndpoint": {
    "Url": "",
    "HttpTimeoutInSeconds": 700,
    "PolicyTimeoutInSeconds": 250,
    "HandlerLifetime": 5,
    "DefaultConnectionLimit": 10
  },
  "OrderActions": [
    {
      "Action": "ACCEPTED",
      "NewStatus": "ACCEPTED",
      "PreviousStatusNeeded": [ "ASSIGNED" ],
      "ResetExecutingState": false
    },
    {
      "Action": "NEW", // refuse or reassign
      "NewStatus": "NEW",
      "PreviousStatusNeeded": [ "ASSIGNED", "ACCEPTED" ],
      "ResetExecutingState": true
    },
    {
      "Action": "DELIVERED",
      "NewStatus": "DELIVERED",
      "PreviousStatusNeeded": [ "ASSIGNED", "ACCEPTED", "ABSENT" ],
      "ResetExecutingState": false
    },
    {
      "Action": "ABSENT",
      "NewStatus": "ABSENT",
      "PreviousStatusNeeded": [ "ASSIGNED", "ACCEPTED" ],
      "ResetExecutingState": false
    },
    {
      "Action": "CANCELLED",
      "NewStatus": "CANCELLED",
      "PreviousStatusNeeded": [ "ASSIGNED", "ACCEPTED", "ABSENT" ],
      "ResetExecutingState": false
    },
    {
      "Action": "REFUSED",
      "NewStatus": "REFUSED",
      "PreviousStatusNeeded": [ "ASSIGNED", "ACCEPTED" ],
      "ResetExecutingState": false
    },
    {
      "Action": "REASSIGN",
      "NewStatus": "REASSIGN",
      "PreviousStatusNeeded": [ "ASSIGNED", "ACCEPTED" ],
      "ResetExecutingState": false
    }
  ],
  "CommerceToolsProductsTypesKey": [
    "mourning",
    "product"
  ],
  "SlackAlert": {
    "DefaultChannel": "alerts-ms-fr",
    "SendMessageUrl": "https://slack.com/api/chat.postMessage",
    "BotName": "Error Alert Bot",
    "BotEmoji": ":warning:",
    "MaxRetryAttempts": 3,
    "RetryInitialDelayMs": 1000,
    "TimeoutMs": 10000,
    "IncludeStackTrace": true,
    "EnableFallbackLogging": true,
    "CircuitBreakerThreshold": 5,
    "CircuitBreakerDurationSeconds": 60
  }
}
